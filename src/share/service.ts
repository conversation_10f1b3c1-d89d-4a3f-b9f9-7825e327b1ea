import * as dayjs from 'dayjs';

export const Pagination = async (model: any, filterDto: any, matchFn?: any) => {
  const {
    page = 0,
    perPage = 100,
    sortField = 'createdAt' as string,
    sortOrder = 1,
    ...filter
  } = filterDto;
  let pipeline = model.aggregate();
  pipeline.match(filter);
  if (matchFn) {
    pipeline = matchFn(pipeline);
  }
  if (sortField !== 'createdAt') {
    pipeline.sort({ [sortField]: sortOrder, 'createdAt': -1 });
  } else {
    pipeline.sort({ [sortField]: sortOrder });
  }
  pipeline.group({
    _id: null,
    total: { $sum: 1 },
    data: { $push: '$$ROOT' },
  });
  pipeline.project({
    totalCount: '$total',
    data: { $slice: ['$data', page * perPage, perPage] },
  });
  try {
    const results = await pipeline.exec();
    return results?.[0] ? results[0] : { totalCount: 0, data: [] };
  } catch (error) {
    if (error) {
      console.log(error);
      return { totalCount: 0, data: [] };
    }
  }
};

export const SalaryCal = (start_time_str: string, end_time_str: string, salary_per: number, travel_fee: number) => {
  let rest_hours = 0;
  let final_salary = 0;

  const [h1, mm1] = start_time_str?.split(':');
  const startTime = dayjs()
    ?.set('hour', +h1)
    ?.set('minute', +mm1)
    ?.set('second', 0);

  const [h2, mm2] = end_time_str?.split(':');
  const endTime = dayjs()?.set('hour', +h2)?.set('minute', +mm2)?.set('second', 0);

  let work_hours =
    endTime
      ?.startOf('minute')
      ?.diff(startTime?.startOf('minute'), 'minute') || 0;
  work_hours = work_hours / 60;
  if (work_hours >= 12) {
    rest_hours = 2;
  } else if (work_hours >= 6) {
    rest_hours = 1;
  }
  final_salary = Math.floor(
    parseFloat(
      ((work_hours - rest_hours) * salary_per + travel_fee).toFixed(12),
    ),
  );
  return { work_hours, final_salary, rest_hours };
}

export const ArraysEqual = (a: any, b: any) => {
  if (a === b) return true;
  if (a == null || b == null) return false;
  if (a.length !== b.length) return false;
  for (var i = 0; i < a.length; ++i) {
    if (a[i] !== b[i]) return false;
  }
  return true;
}