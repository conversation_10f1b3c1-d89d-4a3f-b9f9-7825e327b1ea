import { mongoose, ReturnModelType } from '@typegoose/typegoose';

/* 集合对象基类 */
export class BaseModel {
  _id: mongoose.Types.ObjectId;
  get id() {
    return this._id.toString();
  }

  // get doc_created_at() {
  //   return this._id.getTimestamp();
  // }

  public static findByIds<T extends typeof BaseModel>(this: ReturnModelType<T>, ids: any[]) {
    const query = {
      _id: {
        $in: ids,
      },
    };
    return this.find(query);
  }

  public static countByIds(this: ReturnModelType<typeof BaseModel>, ids: any[]) {
    const query = {
      _id: {
        $in: ids,
      },
    };
    return this.countDocuments(query);
  }
}
