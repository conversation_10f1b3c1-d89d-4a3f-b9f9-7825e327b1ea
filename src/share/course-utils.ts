/**
 * Utility functions for course-related calculations
 */

interface CourseWithExpiredDate {
  expired_date?: Date | null;
  [key: string]: any;
}

/**
 * Calculates the maximum expired_date from an array of courses
 * @param courses Array of courses with expired_date field
 * @returns The maximum expired_date or null if no valid dates found
 */
export function calculateCourseExpiredDate(courses: CourseWithExpiredDate[]): Date | null {
  if (!courses || courses.length === 0) {
    return null;
  }

  // Filter out courses with null/undefined expired_date and convert to Date objects
  const validDates = courses
    .map(course => course.expired_date)
    .filter((date): date is Date => date != null && date instanceof Date)
    .filter(date => !isNaN(date.getTime())); // Filter out invalid dates

  if (validDates.length === 0) {
    return null;
  }

  // Find the maximum date
  return new Date(Math.max(...validDates.map(date => date.getTime())));
}

/**
 * Updates the course_expired_date field for a student object
 * @param student Student object with courses array
 * @returns Updated student object with course_expired_date field
 */
export function updateStudentCourseExpiredDate<T extends { courses: CourseWithExpiredDate[]; course_expired_date?: Date | null }>(
  student: T
): T {
  const maxExpiredDate = calculateCourseExpiredDate(student.courses);
  return {
    ...student,
    course_expired_date: maxExpiredDate
  };
}
