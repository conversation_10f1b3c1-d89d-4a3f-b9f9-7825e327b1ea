import { NotFoundException } from "@nestjs/common";
import { Document, FilterQuery, Model, UpdateQuery } from "mongoose";

export abstract class EntityRepository<T extends Document> {
  constructor(protected readonly entityModel: Model<T>) { }

  async findOne(entityFilterQuery: FilterQuery<T>, projection?: Record<string, unknown>): Promise<T | null> {
    try {
      const record = await this.entityModel.findOne(entityFilterQuery, {
        __v: 0,
        ...projection
      }).exec();
      if (!record) {
        throw new NotFoundException(`Record with ${JSON.stringify(entityFilterQuery)} not found!`);
      }
      return record;
    } catch (error) {
      throw error;
    }
  }

  async find(entityFilterQuery: FilterQuery<T>): Promise<any> {
    const modelNames = `${this.entityModel.modelName.charAt(0).toLowerCase()}${this.entityModel.modelName.slice(1)}s`;
    try {
      const {
        page = 0,
        perPage = 100,
        sortField = 'createdAt',
        sortOrder = 1,
        ...filter
      } = entityFilterQuery;
      const pipeline = this.entityModel.aggregate();
      pipeline.match(filter);
      pipeline.sort({ [sortField]: sortOrder });
      pipeline.group({
        _id: null,
        total: { $sum: 1 },
        [modelNames]: { $push: '$$ROOT' },
      });
      pipeline.project({
        _id: 0,
        totalCount: '$total',
        [modelNames]: { $slice: [`$${[modelNames]}`, page * perPage, perPage] },
      });
      const results = await pipeline.exec();
      return results?.[0] ? results[0] : { totalCount: 0, [modelNames]: [] };
    } catch (error) {
      if (error) {
        return { totalCount: 0, [modelNames]: [] };
      }
    }
  }

  async create(createEntityData: unknown): Promise<T> {
    const entity = new this.entityModel(createEntityData);
    return entity.save();
  }

  async update(entityFilterQuery: FilterQuery<T>, updateEntityData: UpdateQuery<unknown>): Promise<any> {
    try {
      const updateResult = await this.entityModel.updateOne(
        entityFilterQuery,
        { $set: updateEntityData },
      );
      return updateResult.modifiedCount === 0
        ? { message: '更新失败' }
        : { message: '更新成功' };
    } catch (error) {
      return { message: '更新失败' };
    }

  }

  async deleteMany(entityFilterQuery: FilterQuery<T>): Promise<number> {
    const deleteResult = await this.entityModel.deleteMany(entityFilterQuery);
    return deleteResult.deletedCount;
  }
}