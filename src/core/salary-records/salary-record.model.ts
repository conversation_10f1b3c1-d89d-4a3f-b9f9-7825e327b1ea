import { prop, Ref } from '@typegoose/typegoose';
import { User } from '../users/user.model';

export class SalaryRecord {
  // 申报人
  @prop({ ref: 'User' })
  public user?: Ref<User>;

  @prop()
  public user_fullname_cn: string;

  // 审核人
  @prop({ ref: 'User' })
  public check_user?: Ref<User>;

  // 小助手审核人
  @prop({ ref: 'User' })
  public sub_check_user?: Ref<User>;

  // 申报状态
  @prop({ default: 1 })
  public apply_status: number; // 1未提交 2待审核 3通过 4驳回

  // 审核状态
  @prop()
  public check_status: number; // 1审核中 2待定 3通过 4驳回

  // 转让审核状态
  @prop()
  public sub_check_status: number; // 1转让审核中 2转让审核待定 3转让审核通过 4转让审核驳回

  // 提交时间
  @prop()
  public apply_date: Date;

  // 审核时间
  @prop()
  public check_date: Date;

  // 工作日期
  @prop()
  public work_date: Date;

  // 部门
  @prop()
  public department_id: number;

  // 部门
  @prop()
  public department_ids: number[];

  // 申报类型: 1时长 2件数 3字数 4人数 5仅交通费 6定给 7其他
  @prop()
  public apply_type: number;

  // 单价
  @prop()
  public salary_per: number;

  // 开始时间
  @prop()
  public start_time: Date;

  // 结束时间
  @prop()
  public end_time: Date;

  // 开始时间str
  @prop()
  public start_time_str: string;

  // 结束时间str
  @prop()
  public end_time_str: string;

  // 人数/件数/字数
  @prop()
  public amount: number;

  // 交通费起点
  @prop()
  public travel_start: string;

  // 交通费终点
  @prop()
  public travel_end: string;

  // 交通费
  @prop({ default: 0 })
  public travel_fee: number;

  // 劳动时间
  @prop()
  public work_hours: number;

  // 休息时间
  @prop()
  public rest_hours: number;

  // 此项工作收入额
  @prop()
  public final_salary: number;

  // 工作内容
  @prop()
  public work_content: string;

  // 备注
  @prop()
  public memo: string;

  // 上传文件
  @prop()
  public file_links: string[];
}

// TOASK:
// 审核状态栏的表示：有转让状态的时候，把实际审核状态和转让状态都显示吗
// 记录操作时间点=>所有的操作都需要记录吗（status or sub_status发生改变时，记录操作人，操作时间，和更改状态）

// 转让小助手在员工管理里面，设置user身份。
