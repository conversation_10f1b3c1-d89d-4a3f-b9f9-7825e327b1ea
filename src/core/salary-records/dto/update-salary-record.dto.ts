import { Transform, Type } from 'class-transformer';
import { IsArray, IsDate, IsNumber, IsOptional, IsString } from 'class-validator';
import mongoose from 'mongoose';
import { ListDto } from 'src/share/dto';

export class UpdateSalaryRecordDto extends ListDto {
  @IsOptional()
  @Transform(({ value }) => new mongoose.Types.ObjectId(value))
  user: string;

  @IsOptional()
  @Transform(({ value }) => new mongoose.Types.ObjectId(value))
  check_user: string;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => Number(value))
  department_id: number;

  @IsOptional()
  @Type(() => Number)
  @IsArray()
  department_ids: number[];

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => Number(value))
  apply_type: number;

  @IsNumber()
  @Transform(({ value }) => Number(value))
  @IsOptional()
  salary_per: number;

  @IsDate()
  @Type(() => Date)
  @IsOptional()
  work_date: Date;

  @IsDate()
  @Type(() => Date)
  @IsOptional()
  start_time: Date;

  @IsDate()
  @Type(() => Date)
  @IsOptional()
  end_time: Date;

  @IsString()
  @IsOptional()
  start_time_str: string;

  @IsString()
  @IsOptional()
  end_time_str: string;

  @IsNumber()
  @Transform(({ value }) => Number(value))
  @IsOptional()
  work_hours: number;

  @IsNumber()
  @Transform(({ value }) => Number(value))
  @IsOptional()
  rest_hours: number;

  @IsNumber()
  @Transform(({ value }) => Number(value))
  @IsOptional()
  amount: number;

  @IsString()
  @IsOptional()
  travel_start: string;

  @IsString()
  @IsOptional()
  travel_end: string;

  @IsNumber()
  @Transform(({ value }) => Number(value))
  @IsOptional()
  travel_fee: number;

  @IsNumber()
  @Transform(({ value }) => Number(value))
  @IsOptional()
  final_salary: number;

  @IsString()
  @IsOptional()
  work_content: string;

  @IsString()
  @IsOptional()
  memo: string;

  @IsOptional()
  file_links: string[];

  @IsNumber()
  @Transform(({ value }) => Number(value))
  @IsOptional()
  apply_status: number;

  @IsNumber()
  @Transform(({ value }) => Number(value))
  @IsOptional()
  check_status: number;

  @IsNumber()
  @Transform(({ value }) => Number(value))
  @IsOptional()
  sub_check_status: number;
}
