import { Transform, Type } from 'class-transformer';
import { IsArray, IsBoolean, IsDate, IsNumber, IsOptional } from 'class-validator';
import mongoose from 'mongoose';
import { ListDto } from 'src/share/dto';

export class FilterSalaryRecordDto extends ListDto {
  @Transform(({ value }) => new mongoose.Types.ObjectId(value))
  @IsOptional()
  user: string;

  @Transform(({ value }) => new mongoose.Types.ObjectId(value))
  @IsOptional()
  check_user: string;

  @Transform(({ value }) => new mongoose.Types.ObjectId(value))
  @IsOptional()
  sub_check_user: string;

  @IsOptional()
  work_date: Object;

  @IsOptional()
  apply_date: Object;

  @IsOptional()
  check_date: Object;

  @IsOptional()
  apply_status: number[];

  @IsOptional()
  check_status: number[];

  @IsOptional()
  sub_check_status: number[];

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => Number(value))
  department_id: number;

  @IsOptional()
  @Type(() => Number)
  @IsArray()
  department_ids: number;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => Number(value))
  apply_type: number;
}
