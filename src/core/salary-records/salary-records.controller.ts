import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { DtoPipe } from 'src/pipes/dto.pipe';
import { CreateSalaryRecordDto } from './dto/create-salary-record.dto';
import { FilterSalaryRecordDto } from './dto/filter-salary-record.dto';
import { GetMonthStatForCheckDto } from './dto/get-month-stat-for-check.dto';
import { UpdateSalaryRecordDto } from './dto/update-salary-record.dto';
import { SalaryRecordsService } from './salary-records.service';

@Controller('salary-records')
export class SalaryRecordsController {
  constructor(private salaryRecordsService: SalaryRecordsService) {}

  @Get()
  @UsePipes(new ValidationPipe({ transform: true }))
  @UsePipes(DtoPipe)
  getSalaryRecords(@Query() dto: FilterSalaryRecordDto) {
    return this.salaryRecordsService.getSalaryRecords(dto);
  }

  @Get('/:id')
  getSalaryRecordById(@Param('id') id: string) {
    return this.salaryRecordsService.getSalaryRecordById(id);
  }

  @Post()
  @UsePipes(new ValidationPipe({ transform: true }))
  createSalaryRecord(@Body() dto: CreateSalaryRecordDto) {
    return this.salaryRecordsService.createSalaryRecord(dto);
  }

  @Post('/check-month-stat/:id')
  @UsePipes(new ValidationPipe({ transform: true }))
  getMonthStatForCheck(
    @Param('id') userId: string,
    @Body() dto: GetMonthStatForCheckDto,
  ) {
    return this.salaryRecordsService.getMonthStatForCheck(userId, dto);
  }

  @Post('/apply/:id')
  @UsePipes(new ValidationPipe({ transform: true }))
  applySalaryRecord(@Param('id') userId: string) {
    return this.salaryRecordsService.applySalaryRecord(userId);
  }

  @Post('/check/:id')
  @UsePipes(new ValidationPipe({ transform: true }))
  checkSalaryRecord(@Param('id') userId: string) {
    return this.salaryRecordsService.checkSalaryRecord(userId);
  }

  @Patch('/:id')
  @UsePipes(new ValidationPipe({ transform: true }))
  updateSalaryRecord(
    @Param('id') id: string,
    @Body() dto: UpdateSalaryRecordDto,
  ) {
    return this.salaryRecordsService.updateSalaryRecord(id, dto);
  }

  @Delete('/:id')
  deleteSalaryRecord(@Param('id') id: string) {
    return this.salaryRecordsService.deleteSalaryRecord(id);
  }
}
