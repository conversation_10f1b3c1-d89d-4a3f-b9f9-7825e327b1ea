import {
  ForbiddenException,
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { ReturnModelType } from '@typegoose/typegoose';
import { InjectModel } from 'nestjs-typegoose';
import { Pagination } from 'src/share/service';
import { CreateSalaryRecordDto } from './dto/create-salary-record.dto';
import { FilterSalaryRecordDto } from './dto/filter-salary-record.dto';
import { UpdateSalaryRecordDto } from './dto/update-salary-record.dto';
import { SalaryRecord } from './salary-record.model';
import * as dayjs from 'dayjs';
import mongoose from 'mongoose';
import { User } from '../users/user.model';
import { GetMonthStatForCheckDto } from './dto/get-month-stat-for-check.dto';

@Injectable()
export class SalaryRecordsService {
  constructor(
    @InjectModel(SalaryRecord)
    private readonly salaryRecordModel: ReturnModelType<typeof SalaryRecord>,
    @InjectModel(User)
    private readonly userModel: ReturnModelType<typeof User>,
  ) { }

  private readonly logger = new Logger(SalaryRecordsService.name);

  async getSalaryRecordById(id: string): Promise<any> {
    try {
      const salaryRecord = await this.salaryRecordModel.findById(id);
      if (!salaryRecord) {
        return {
          message: '申告記録が存在しません。',
        };
      }
      return salaryRecord;
    } catch (error) {
      this.logger.error(error.message);
      throw new InternalServerErrorException();
    }
  }

  async getSalaryRecords(dto: FilterSalaryRecordDto): Promise<any> {
    // const salaryRecords2 = await this.salaryRecordModel.updateMany({ apply_status: 2, apply_date: { $exists: false }, $and: [{ work_date: { $gte: new Date('2023-07-01') } }, { work_date: { $lte: new Date('2023-08-01') } }] }, { $set: { apply_date: new Date('2023-09-03') } });
    // return;
    // apply_status = 4 && 时间小于本月1号的, 不return；新增1个param来判定是否展示
    const { totalCount, data: salaryRecords } = await Pagination(
      this.salaryRecordModel,
      dto
    );
    return { totalCount, salaryRecords };
  }

  async getMonthStatForCheck(
    userId: string,
    dto: GetMonthStatForCheckDto,
  ): Promise<any> {
    const pipeline = this.salaryRecordModel.aggregate();
    if (dto.isSub) {
      pipeline.match({ sub_check_user: new mongoose.Types.ObjectId(userId) });
    } else {
      pipeline.match({ check_user: new mongoose.Types.ObjectId(userId) });
    }
    const thisMonthStart = dayjs().startOf('month').subtract(9, 'hour').toDate();
    // const thisMonthEnd = dayjs().endOf('month').toDate();
    pipeline.sort({ ['apply_date']: -1 });
    pipeline.match({ apply_date: { $gte: thisMonthStart } });
    pipeline.group({
      _id: { year: { $year: '$work_date' }, month: { $month: '$work_date' } },
      count: { $sum: 1 },
      undoneCount: {
        $sum: {
          $cond: [
            { $eq: ['$apply_status', 2] }, 1, 0
          ]
        }
      }
    });
    pipeline.sort({ '_id.year': 1, '_id.month': 1 });
    const results = await pipeline.exec();
    return results;
  }

  async createSalaryRecord(dto: CreateSalaryRecordDto, isVip: boolean = false) {
    try {
      const oldSalaryRecords = await this.salaryRecordModel.find({
        user: dto.user,
        apply_type: 1,
        work_date: dto.work_date,
        apply_status: { $ne: 4 },
        $or: [
          {
            start_time_str: {
              $gte: dto.start_time_str,
              $lt: dto.end_time_str,
            },
          },
          {
            end_time_str: {
              $gt: dto.start_time_str,
              $lte: dto.end_time_str,
            },
          },
        ],
      });
      if (oldSalaryRecords.length > 0) {
        if (isVip) {
          await this.salaryRecordModel.deleteMany({ _id: { $in: oldSalaryRecords.map(item => item._id) } })
        } else {
          throw new ForbiddenException('工作时间区间有冲突！');
        }
      }
      const salaryRecord = new this.salaryRecordModel(dto);
      const user = await this.userModel.findById(dto.user);
      salaryRecord.user_fullname_cn = `${user.last_name_cn}${user.first_name_cn}`;
      await salaryRecord.save();
      return { _id: salaryRecord._id };
    } catch (error) {
      throw error;
    }
  }

  async updateSalaryRecord(id: string, dto: UpdateSalaryRecordDto) {
    try {
      // const salaryRecord = await this.getSalaryRecordById(id);
      // if (dto.status === 2) {
      //   dto['check_date'] = new Date();
      // }
      const result = await this.salaryRecordModel.updateOne(
        { _id: id },
        { $set: dto },
      );
      return result.modifiedCount === 0
        ? { message: '更新失败' }
        : { message: '更新成功' };
    } catch (error) {
      this.logger.error(error.message);
      return { message: '更新失败' };
      // throw new InternalServerErrorException();
    }
  }

  // 完成申报记录
  async applySalaryRecord(userId: string) {
    try {
      const thisMonthStart = dayjs().add(9, 'hour').startOf('month').toDate();
      const result = await this.salaryRecordModel.updateMany(
        {
          user: new mongoose.Types.ObjectId(userId),
          apply_status: 1,
          work_date: { $lt: thisMonthStart },
        },
        {
          $set: {
            apply_status: 2,
            check_status: 1,
            apply_date: new Date(),
          },
        },
      );
      return result.modifiedCount === 0
        ? { message: '更新失败' }
        : { message: '更新成功' };
    } catch (error) {
      this.logger.error(error.message);
      return { message: '更新失败' };
      // throw new InternalServerErrorException();
    }
  }

  // 完成审核记录
  async checkSalaryRecord(userId: string) {
    try {
      const thisMonthStart = dayjs().startOf('month').subtract(9, 'hour').toDate();
      const salaryRecords = await this.salaryRecordModel.find({
        apply_date: { $gte: thisMonthStart },
        check_user: new mongoose.Types.ObjectId(userId),
        check_status: { $in: [3, 4] },
      });
      for (const salaryRecord of salaryRecords) {
        salaryRecord.check_date = new Date();
        salaryRecord.apply_status = salaryRecord.check_status;
        await salaryRecord.save();
      }
      return { message: '更新成功' };
    } catch (error) {
      this.logger.error(error.message);
      return { message: '更新失败' };
      // throw new InternalServerErrorException();
    }
  }

  async deleteSalaryRecord(id: string): Promise<any> {
    try {
      const result = await this.salaryRecordModel.deleteOne({ _id: id }).exec();
      return result.deletedCount;
    } catch (error) {
      this.logger.error(error.message);
      throw new InternalServerErrorException();
    }
  }
}
