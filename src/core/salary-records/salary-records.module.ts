import { Modu<PERSON> } from '@nestjs/common';
import { TypegooseModule } from 'nestjs-typegoose';
import { User } from '../users/user.model';
import { SalaryRecord } from './salary-record.model';
import { SalaryRecordsController } from './salary-records.controller';
import { SalaryRecordsService } from './salary-records.service';

@Module({
  imports: [
    TypegooseModule.forFeature([
      { typegooseClass: SalaryRecord, schemaOptions: { timestamps: true } },
      User,
    ]),
  ],
  controllers: [SalaryRecordsController],
  providers: [SalaryRecordsService],
  exports: [SalaryRecordsService]
})
export class SalaryRecordsModule { }
