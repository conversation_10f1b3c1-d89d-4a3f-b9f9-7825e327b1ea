import { Body, Controller, Delete, Get, Param, Patch, Post, Query, UsePipes, ValidationPipe } from '@nestjs/common';
import { CreateVipCourseDto } from './dto/create-vip-course.dto';
import { FilterVipCourseDto } from './dto/filter-vip-course.dto';
import { UpdateVipCourseDto } from './dto/update-vip-course.dto';
import { VipCoursesService } from './vip-courses.service';
import { DtoPipe } from 'src/pipes/dto.pipe';
import { FinishVipCoursesDto } from './dto/finish-vip-courses.dto';

@Controller('vip-courses')
export class VipCoursesController {
  constructor(private vipCoursesService: VipCoursesService) {}

  @Get()
  @UsePipes(new ValidationPipe({ transform: true }))
  @UsePipes(DtoPipe)
  getVipCourses(@Query() dto: FilterVipCourseDto) {
    return this.vipCoursesService.getVipCourses(dto);
  }

  @Get('/:id')
  getVipCourseById(@Param('id') id: string) {
    return this.vipCoursesService.getVipCourseById(id);
  }

  @Post()
  @UsePipes(new ValidationPipe({ transform: true }))
  createVipCourse(@Body() dto: CreateVipCourseDto) {
    return this.vipCoursesService.createVipCourse(dto);
  }

  @Post('/finish')
  @UsePipes(new ValidationPipe({ transform: true }))
  finishVipCourses(@Body() dto: FinishVipCoursesDto) {
    return this.vipCoursesService.finishVipCourses(dto);
  }

  @Patch('/:id')
  @UsePipes(new ValidationPipe({ transform: true }))
  updateVipCourse(@Param('id') id: string, @Body() dto: UpdateVipCourseDto) {
    return this.vipCoursesService.updateVipCourse(id, dto);
  }

  @Delete('/:id')
  deleteVipCourse(@Param('id') id: string) {
    return this.vipCoursesService.deleteVipCourse(id);
  }
}
