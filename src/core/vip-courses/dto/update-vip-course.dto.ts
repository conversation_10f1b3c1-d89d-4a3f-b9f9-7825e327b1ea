import { Transform, Type } from "class-transformer";
import { IsDate, <PERSON>N<PERSON>ber, IsOptional, IsString } from "class-validator";
import { Types } from "mongoose";
import { toMongoObjectId } from "src/share/validator";

export class UpdateVipCourseDto {
  @Transform(toMongoObjectId)
  @IsOptional()
  student: Types.ObjectId;

  @Transform(toMongoObjectId)
  @IsOptional()
  vip_teacher: Types.ObjectId;

  @Type(() => Date)
  @IsDate()
  @IsOptional()
  vip_date: Date;

  @IsString()
  @IsOptional()
  start_date_str: string;

  @IsString()
  @IsOptional()
  end_date_str: string;

  @IsNumber()
  @Transform(({ value }) => Number(value))
  @IsOptional()
  duration: number;

  @IsString()
  @IsOptional()
  note: string;
}