import { Transform, Type } from "class-transformer";
import { <PERSON>D<PERSON>, <PERSON>N<PERSON>ber, IsOptional } from "class-validator";
import { Types } from "mongoose";
import { ListDto } from "src/share/dto";
import { toMongoObjectId } from "src/share/validator";

export class FilterVipCourseDto extends ListDto {
  @Transform(toMongoObjectId)
  @IsOptional()
  student: Types.ObjectId;

  @Transform(toMongoObjectId)
  @IsOptional()
  vip_teacher: Types.ObjectId;

  @IsOptional()
  vip_date: Object;
}