import { Transform, Type } from "class-transformer";
import { <PERSON><PERSON><PERSON>y, <PERSON>Date, <PERSON>N<PERSON>ber, IsOptional, IsString } from "class-validator";
import { Types } from "mongoose";
import { toMongoObjectId } from "src/share/validator";

export class CreateVipCourseDto {
  @Transform(toMongoObjectId)
  @IsOptional()
  student: Types.ObjectId;

  @Transform(toMongoObjectId)
  vip_teacher: Types.ObjectId;

  @Type(() => Date)
  @IsDate()
  vip_date: Date;

  @IsString()
  start_date_str: string;

  @IsString()
  end_date_str: string;

  @IsNumber()
  @Transform(({ value }) => Number(value))
  duration: number;

  @IsString()
  @IsOptional()
  note: string;
}