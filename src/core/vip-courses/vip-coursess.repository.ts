import { Injectable } from "@nestjs/common";
import { Model } from "mongoose";
import { InjectModel } from "nestjs-typegoose";
import { EntityRepository } from "src/database/entity.repository";
import { VipCourse, VipCourseDocument } from "./vip-course.model";

@Injectable()
export class VipCoursesRepository extends EntityRepository<VipCourseDocument> {
  constructor(@InjectModel(VipCourse) vipCourseModel: Model<VipCourseDocument>) {
    super(vipCourseModel)
  }
}