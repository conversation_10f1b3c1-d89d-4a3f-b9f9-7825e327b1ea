import { Module } from '@nestjs/common';
import { VipCoursesService } from './vip-courses.service';
import { VipCoursesController } from './vip-courses.controller';
import { TypegooseModule } from 'nestjs-typegoose';
import { VipCourse } from './vip-course.model';
import { VipCoursesRepository } from './vip-coursess.repository';
import { StudentsRepository } from '../students/students.repository';
import { Student } from '../students/student.model';
import { User } from '../users/user.model';
import { SalaryRecordsModule } from '../salary-records/salary-records.module';

@Module({
  imports: [
    SalaryRecordsModule,
    TypegooseModule.forFeature([
      {
        typegooseClass: VipCourse,
        schemaOptions: { timestamps: true },
      },
      Student,
      User,
    ]),
  ],
  providers: [VipCoursesService, VipCoursesRepository, StudentsRepository],
  controllers: [VipCoursesController],
})
export class VipCoursesModule {}
