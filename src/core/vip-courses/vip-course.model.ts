import { DocumentType, prop, Ref } from "@typegoose/typegoose";
import { Student } from "../students/student.model";
import { User } from "../users/user.model";

export type VipCourseDocument = DocumentType<VipCourse> & Document;

export class VipCourse {
  @prop({ ref: 'Student' })
  public student: Ref<Student>;

  // 上课老师
  @prop({ ref: 'User' })
  public vip_teacher?: Ref<User>;

  // 上课日期
  @prop()
  public vip_date: Date;

  // 开始时间
  @prop()
  public start_date_str: string;

  // 结束时间
  @prop()
  public end_date_str: string;

  // 讲义时间
  @prop({ default: 0 })
  public duration: number;

  // 备注（工作内容）
  @prop()
  public note: string;

  @prop({ default: 0 })
  public status: number;
}