import { ForbiddenException, Injectable } from '@nestjs/common';
import { CreateVipCourseDto } from './dto/create-vip-course.dto';
import { FilterVipCourseDto } from './dto/filter-vip-course.dto';
import { UpdateVipCourseDto } from './dto/update-vip-course.dto';
import { VipCourse } from './vip-course.model';
import { VipCoursesRepository } from './vip-coursess.repository';
import { StudentsRepository } from '../students/students.repository';
import { InjectModel } from 'nestjs-typegoose';
import { Student } from '../students/student.model';
import { ReturnModelType } from '@typegoose/typegoose';
import { FinishVipCoursesDto } from './dto/finish-vip-courses.dto';
import { User } from '../users/user.model';
import { SalaryCal } from 'src/share/service';
import { SalaryRecordsService } from '../salary-records/salary-records.service';
import mongoose from 'mongoose';
import * as dayjs from 'dayjs';

@Injectable()
export class VipCoursesService {
  constructor(
    private readonly vipCoursesRepository: VipCoursesRepository,
    private readonly studentsRepository: StudentsRepository,

    @InjectModel(VipCourse)
    private readonly vipCourseModel: ReturnModelType<typeof VipCourse>,
    @InjectModel(Student)
    private readonly studentModel: ReturnModelType<typeof Student>,
    @InjectModel(User)
    private readonly userModel: ReturnModelType<typeof User>,

    private salaryRecordsService: SalaryRecordsService,
  ) {}

  async getVipCourseById(id: string): Promise<VipCourse> {
    return this.vipCoursesRepository.findOne({ _id: id });
  }

  async getVipCourses(filterDto: FilterVipCourseDto): Promise<any> {
    return this.vipCoursesRepository.find(filterDto);
  }

  async createVipCourse(dto: CreateVipCourseDto): Promise<any> {
    if (dto.duration) {
      await this.studentModel.updateOne(
        { _id: dto.student },
        { $inc: { vip_used_hours: dto.duration } },
      );
    }
    return this.vipCoursesRepository.create(dto);
  }

  async updateVipCourse(id: string, dto: UpdateVipCourseDto) {
    return this.vipCoursesRepository.update({ _id: id }, dto);
  }

  async deleteVipCourse(id: string): Promise<any> {
    const vipCourse = await this.getVipCourseById(id);
    if (vipCourse) {
      await this.studentModel.updateOne(
        { _id: vipCourse.student },
        { $inc: { vip_used_hours: -vipCourse.duration } },
      );
    }
    return this.vipCoursesRepository.deleteMany({ _id: id });
  }

  async finishVipCourses(dto: FinishVipCoursesDto): Promise<any> {
    try {
      const vipCourses = await this.vipCourseModel.find({
        _id: { $in: dto.ids },
      });
      let count = 0;
      for (const vipCourse of vipCourses) {
        const user = await this.userModel.findById(vipCourse.vip_teacher);
        const student = await this.studentModel.findById(vipCourse.student);
        const applyDate = dayjs(vipCourse.vip_date).add(1, 'month').set('date', 3);
        if (user.vip_salary) {
          // 自动计算工资
          const salaryObj = SalaryCal(
            vipCourse.start_date_str,
            vipCourse.end_date_str,
            user.vip_salary,
            0,
          );
          const createSalaryDto = {
            apply_status: 2,
            check_status: 1,
            apply_date: applyDate,
            user: user._id,
            check_user: '63ae29bfdb3ac885b788407a',
            department_ids: student.student_type === 1 ? [1, 3] : [2, 3],
            apply_type: 1,
            salary_per: user.vip_salary,
            work_date: vipCourse.vip_date,
            start_time_str: vipCourse.start_date_str,
            end_time_str: vipCourse.end_date_str,
            work_hours: salaryObj.work_hours,
            rest_hours: salaryObj.rest_hours,
            final_salary: salaryObj.final_salary,
            work_content: vipCourse.note,
          };
          await this.salaryRecordsService.createSalaryRecord(createSalaryDto, true);
          vipCourse.status = 1;
          await vipCourse.save();
        } else {
          count += 1;
        }
      }
      if (count > 0) {
        throw new ForbiddenException(`有${count}条尚未设置VIP工资`);
      } else {
        return { message: '自动申报成功' };
      }
    } catch (error) {
      console.log(error);
      throw error;
    }
  }
}
