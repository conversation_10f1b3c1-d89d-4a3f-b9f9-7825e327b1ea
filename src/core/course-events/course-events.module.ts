import { Module } from '@nestjs/common';
import { CourseEventsController } from './course-events.controller';
import { CourseEventsService } from './course-events.service';
import { TypegooseModule } from 'nestjs-typegoose';
import { CourseEvent } from './course-event.model';
import { CourseEventsRepository } from './course-events.repository';
import { ZoomSetting } from '../zoom-settings/zoom-setting.model';
import { SalaryRecordsModule } from '../salary-records/salary-records.module';
import { User } from '../users/user.model';

@Module({
  imports: [
    SalaryRecordsModule,
    TypegooseModule.forFeature([{
      typegooseClass: CourseEvent, schemaOptions: { timestamps: true }
    }, ZoomSetting, User])
  ],
  controllers: [CourseEventsController],
  providers: [CourseEventsService, CourseEventsRepository]
})
export class CourseEventsModule { }
