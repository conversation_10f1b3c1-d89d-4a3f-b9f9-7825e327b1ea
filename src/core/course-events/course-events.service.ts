import { Injectable } from '@nestjs/common';
import { CourseEventsRepository } from './course-events.repository';
import { InjectModel } from 'nestjs-typegoose';
import { CourseEvent } from './course-event.model';
import { ReturnModelType } from '@typegoose/typegoose';
import { AutoCreateCourseEventsDto } from './dto/auto-create-course-events.dto';
import { BulkCreateCourseEventsDto } from './dto/bulk-create-course-events.dto';
import { FilterCourseEventsDto } from './dto/filter-course-events.dto';
import * as dayjs from 'dayjs';
import { MultiUpdateCourseEventDto } from './dto/multi-update-course-event.dto';
import mongoose from 'mongoose';
import { Pagination, SalaryCal } from 'src/share/service';
import { ZoomSetting } from '../zoom-settings/zoom-setting.model';
import { UpdateCourseEventDto } from './dto/update-course-event.dto';
import { CreateCourseEventDto } from './dto/create-course-event.dto';
import { User } from '../users/user.model';
import { SalaryRecordsService } from '../salary-records/salary-records.service';

@Injectable()
export class CourseEventsService {
  constructor(
    private readonly courseEventsRepository: CourseEventsRepository,

    @InjectModel(CourseEvent)
    private readonly courseEventModel: ReturnModelType<typeof CourseEvent>,
    @InjectModel(ZoomSetting)
    private readonly zoomSettingModel: ReturnModelType<typeof ZoomSetting>,
    @InjectModel(User)
    private readonly userModel: ReturnModelType<typeof User>,

    private salaryRecordsService: SalaryRecordsService
  ) { }

  async getCourseEventById(id: string): Promise<CourseEvent> {
    return this.courseEventsRepository.findOne({ _id: id });
  }

  async createCourseEvent(dto: CreateCourseEventDto): Promise<any> {
    try {
      const courseEvent = await this.courseEventsRepository.create(dto);
      return { courseEvent, message: '课程事件创建成功' };
    } catch (error) {
      console.log(error);
      return { message: '课程事件创建失败' };
    }
  }

  async getCourseEvents(filterDto: FilterCourseEventsDto): Promise<any> {
    const { perPage, page, sortField, sortOrder, category, ...filter } =
      filterDto;

    // Debug logging to understand the data flow
    console.log('FilterDto received:', filterDto);
    console.log('Category after DtoPipe:', category);
    console.log('Remaining filter:', filter);

    const matchFn = (pipeline: any) => {
      if (category) {
        // category is already transformed by DtoPipe from category_in parameter
        // It contains { $in: nestedArrays } structure where nestedArrays is [["数学","数学Ⅱ","正课"]]
        // We need to extract the nested arrays from the $in property for our exact array matching query
        const categoryFilters = category.$in;

        console.log('Category filters extracted:', categoryFilters);

        // Use exact array matching - find documents where any sub-array in category exactly matches
        // any of the arrays in categoryFilters
        const matchQuery = {
          category: {
            $elemMatch: {
              $in: categoryFilters
            }
          }
        };

        console.log('MongoDB match query:', JSON.stringify(matchQuery, null, 2));
        pipeline.match(matchQuery);
      }
      return pipeline;
    };
    const { totalCount, data: courseEvents } = await Pagination(
      this.courseEventModel,
      {
        perPage,
        page,
        sortField,
        sortOrder,
        ...filter,
      },
      matchFn,
    );
    return { totalCount, courseEvents };
  }

  async autoCreateCourseEvents(dto: AutoCreateCourseEventsDto): Promise<any> {
    try {
      const { week_days, week_times_start, week_times_end, durations, count, first_date, category, name, teacher, note, check_user, department_ids } = dto;
      let current_date = dayjs(first_date);
      if (week_days.length > 0) {
        const creates = [];
        for (let i = 0; i < count; i++) {
          while (!week_days.includes(current_date.day())) {
            current_date = current_date.add(1, 'day');
          }
          creates.push({
            category,
            name,
            teacher,
            check_user,
            department_ids,
            note,
            date: current_date.toDate(),
            start_date_str: week_times_start[current_date.day()],
            end_date_str: week_times_end[current_date.day()],
            hours: durations[current_date.day()]
          })
          current_date = current_date.add(1, 'day');
        }
        if (creates.length > 0) {
          const results = await this.courseEventModel.insertMany(creates);
          await this.setZoomAccounts(results);
        }
        return { message: '创建成功' };
      }

    } catch (error) {
      console.log(error);
      return { message: '创建失败' };
    }
  }

  async bulkCreateCourseEvents(dto: BulkCreateCourseEventsDto): Promise<any> {
    try {
      const { category, name, teacher, note, check_user, department_ids, work_content, course_dates } = dto;

      if (course_dates.length > 0) {
        const creates = course_dates.map(courseDate => ({
          category,
          name,
          teacher,
          check_user,
          department_ids,
          note,
          work_content,
          date: courseDate.date,
          start_date_str: courseDate.start_date_str,
          end_date_str: courseDate.end_date_str,
          hours: courseDate.hours
        }));

        const results = await this.courseEventModel.insertMany(creates);
        await this.setZoomAccounts(results);
        return { message: '批量创建成功' };
      }

      return { message: '没有提供课程日期' };
    } catch (error) {
      console.log(error);
      return { message: '批量创建失败' };
    }
  }

  async setZoomAccounts(courseEvents: any) {
    try {
      const zoomSettings = await this.zoomSettingModel.find();
      const courseEventDates = courseEvents.map(item => item.date);
      const otherCourseEvents: CourseEvent[] = await this.courseEventModel.find({ date: { $in: courseEventDates }, zoom_account: { $exists: true } });
      const canZoomAccounts: { [key: string]: string[] } = {};
      const courseEventIds = courseEvents.map(item => String(item._id));
      for (const zoomSetting of zoomSettings) {
        canZoomAccounts[zoomSetting.account] = courseEventIds;
      }
      for (const otherCourseEvent of otherCourseEvents) {
        // console.log(otherCourseEvent.date, courseEvents[0].date, otherCourseEvent.date.toString() === courseEvents[0].date.toString())
        const found = courseEvents.find(item => otherCourseEvent.date.toString() === item.date.toString() && otherCourseEvent.start_date_str <= item.end_date_str && otherCourseEvent.end_date_str >= item.start_date_str);
        if (found) {
          const key = otherCourseEvent.zoom_account;
          canZoomAccounts[key] = canZoomAccounts[key].filter(item => item !== String(found._id));
        }
      }
      const sorted = Object.entries(canZoomAccounts).sort(([, a], [, b]) => b.length - a.length);

      for (const courseEvent of courseEvents) {
        for (const s of sorted) {
          if (s[1].includes(String(courseEvent._id))) {
            await this.courseEventModel.updateOne({ _id: courseEvent._id }, { zoom_account: s[0] });
            break;
          }
        }
      }
    } catch (error) {
      console.log(error);
    }
    // { "zoom1": [id1,id2, ..id8] }
  }

  async multiUpdateCourseEvents(dto: MultiUpdateCourseEventDto): Promise<any> {
    try {
      const { ids, ...updateDto } = dto;
      const mongoIds = ids.map(i => new mongoose.Types.ObjectId(i));
      const updateResult = await this.courseEventModel.updateMany({ _id: { $in: mongoIds } }, { $set: updateDto });
      return updateResult.modifiedCount === 0
        ? { message: '更新失败' }
        : { message: '更新成功' };
    } catch (error) {
      console.log(error);
      return { message: '更新失败' };
    }
  }

  async updateCourseEvent(id: string, dto: UpdateCourseEventDto) {
    return this.courseEventsRepository.update({ _id: id }, dto);
  }

  async finishCourseEvent(id: string) {
    try {
      const courseEvent = await this.courseEventModel.findById(id);
      const user = await this.userModel.findById(courseEvent.teacher);
      // Flatten the nested category arrays and join for salary matching
      const flattenedCategories = courseEvent.category.flat();
      const baseSalary = user.base_salary.find(item => item.name.join(',') === flattenedCategories.join(','));
      const applyDate = dayjs(courseEvent.date).add(1, 'month').set('date', 3);
      if (baseSalary) {
        // 自动计算工资
        const salaryObj = SalaryCal(courseEvent.start_date_str, courseEvent.end_date_str, baseSalary.salary, 0);
        const createSalaryDto = {
          apply_status: 2,
          check_status: 1,
          user: user._id,
          apply_date: applyDate,
          check_user: String(courseEvent.check_user),
          department_ids: courseEvent.department_ids,
          apply_type: 1,
          salary_per: baseSalary.salary,
          work_date: courseEvent.date,
          start_time_str: courseEvent.start_date_str,
          end_time_str: courseEvent.end_date_str,
          work_hours: salaryObj.work_hours,
          rest_hours: salaryObj.rest_hours,
          final_salary: salaryObj.final_salary,
          work_content: courseEvent.work_content || courseEvent.name,
        }
        await this.salaryRecordsService.createSalaryRecord(createSalaryDto, true);
        courseEvent.status = 1;
        await courseEvent.save();
        return { message: '自动申报成功' }
      }
      return { message: '尚未设置对应基本工资' }
    } catch (error) {
      console.log(error);
      return { message: '自动申报失败' }
    }
  }

  async deleteCourseEvent(id: string): Promise<any> {
    // const courseEvent = await this.getCourseEventById(id);
    return this.courseEventsRepository.deleteMany({ _id: id });
  }

  async toggleCourseEventNotification(id: string): Promise<any> {
    try {
      const courseEvent = await this.courseEventModel.findById(id);
      if (!courseEvent) {
        return { message: '课程事件未找到' };
      }

      // Toggle the notification status
      courseEvent.has_notification = !courseEvent.has_notification;
      const updatedCourseEvent = await courseEvent.save();

      const statusMessage = updatedCourseEvent.has_notification ? '通知已开启' : '通知已关闭';

      return {
        courseEvent: updatedCourseEvent,
        message: statusMessage
      };
    } catch (error) {
      console.log(error);
      return { message: '切换通知状态失败' };
    }
  }
}
