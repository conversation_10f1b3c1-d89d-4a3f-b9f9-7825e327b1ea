import { Transform, Type } from "class-transformer";
import { Is<PERSON>rray, IsDate, IsNumber, IsOptional, IsString, ValidateNested } from "class-validator";
import { Types } from "mongoose";
import { toMongoObjectId } from "src/share/validator";

export class CourseEventDateDto {
  @Type(() => Date)
  @IsDate()
  date: Date;

  @IsString()
  start_date_str: string;

  @IsString()
  end_date_str: string;

  @IsNumber()
  @Transform(({ value }) => Number(value))
  hours: number;
}

export class BulkCreateCourseEventsDto {
  @IsArray()
  @IsArray({ each: true })
  category: string[][];

  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  work_content: string;

  @Transform(toMongoObjectId)
  teacher: Types.ObjectId;

  @Transform(toMongoObjectId)
  check_user: Types.ObjectId;

  @IsArray()
  department_ids: number[];

  @IsString()
  @IsOptional()
  note: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CourseEventDateDto)
  course_dates: CourseEventDateDto[];
}
