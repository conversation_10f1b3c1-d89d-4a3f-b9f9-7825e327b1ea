import { Transform, Type } from "class-transformer";
import { <PERSON><PERSON>rray, IsDate, IsNumber, IsOptional, IsString } from "class-validator";
import { Types } from "mongoose";
import { toMongoObjectId } from "src/share/validator";

export class MultiUpdateCourseEventDto {
  @IsArray()
  @IsOptional()
  ids: string[];

  @IsArray()
  @IsArray({ each: true })
  @IsOptional()
  category: string[][];

  @IsString()
  @IsOptional()
  name: string;

  @IsNumber()
  @Transform(({ value }) => Number(value))
  @IsOptional()
  status: number;

  @Transform(toMongoObjectId)
  @IsOptional()
  teacher: Types.ObjectId;

  @IsString()
  @IsOptional()
  zoom_account: string;

  @IsString()
  @IsOptional()
  note: string;
}