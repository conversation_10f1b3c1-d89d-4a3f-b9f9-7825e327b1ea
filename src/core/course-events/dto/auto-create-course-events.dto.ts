import { Transform, Type } from "class-transformer";
import { <PERSON><PERSON><PERSON><PERSON>, IsDate, <PERSON>N<PERSON>ber, IsOptional, IsString } from "class-validator";
import { Types } from "mongoose";
import { toMongoObjectId } from "src/share/validator";

export class AutoCreateCourseEventsDto {
  @IsArray()
  @IsArray({ each: true })
  category: string[][];

  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  work_content: string;

  @Transform(toMongoObjectId)
  teacher: Types.ObjectId;

  @Transform(toMongoObjectId)
  check_user: Types.ObjectId;

  @IsArray()
  department_ids: number[];

  @IsString()
  @IsOptional()
  note: string;

  @Type(() => Date)
  @IsDate()
  first_date: Date;

  @IsNumber()
  @Transform(({ value }) => Number(value))
  count: number;

  @IsArray()
  week_days: number[];

  @IsArray()
  week_times_start: string[];

  @IsArray()
  week_times_end: string[];

  @IsArray()
  durations: number[];
}