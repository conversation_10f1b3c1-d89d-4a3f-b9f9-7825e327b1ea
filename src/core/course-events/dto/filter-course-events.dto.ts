import { Transform, Type } from "class-transformer";
import { IsArray, IsOptional } from "class-validator";
import { Types } from "mongoose";
import { ListDto } from "src/share/dto";
import { toMongoObjectId } from "src/share/validator";

export class FilterCourseEventsDto extends ListDto {
  @IsOptional()
  category_in: string;

  @IsOptional()
  category: any; // This will be populated by DtoPipe from category_in

  @IsOptional()
  name: string;

  @Transform(toMongoObjectId)
  @IsOptional()
  teacher: Types.ObjectId;

  @IsOptional()
  zoom_account: string;

  @Type(() => Number)
  @IsOptional()
  @IsArray()
  department_ids: number[];

  @Transform(toMongoObjectId)
  @IsOptional()
  check_user: string;

  @Transform(({ value }) => Number(value))
  @IsOptional()
  status: number;
}