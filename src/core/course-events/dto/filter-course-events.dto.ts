import { Transform, Type } from "class-transformer";
import { <PERSON><PERSON><PERSON>y, IsDate, <PERSON>Number, IsOptional, IsString } from "class-validator";
import { Types } from "mongoose";
import { ListDto } from "src/share/dto";
import { toMongoObjectId } from "src/share/validator";

export class FilterCourseEventsDto extends ListDto {
  @IsOptional()
  categories: string;

  @IsArray()
  @IsArray({ each: true })
  @IsOptional()
  category: string[][];

  @IsOptional()
  name: string;

  @Transform(toMongoObjectId)
  @IsOptional()
  teacher: Types.ObjectId;

  @IsOptional()
  zoom_account: string;

  @Type(() => Number)
  @IsOptional()
  @IsArray()
  department_ids: number[];

  @Transform(toMongoObjectId)
  @IsOptional()
  check_user: string;

  @Transform(({ value }) => Number(value))
  @IsOptional()
  status: number;
}