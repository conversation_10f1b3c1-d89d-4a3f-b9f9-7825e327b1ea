import { Transform, Type } from "class-transformer";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>Date, <PERSON><PERSON><PERSON>ber, IsOptional, IsString } from "class-validator";
import { Types } from "mongoose";
import { ListDto } from "src/share/dto";
import { toMongoObjectId } from "src/share/validator";

export class FilterCourseEventsDto extends ListDto {
  @IsOptional()
  categories_in: string;

  @IsArray()
  @IsArray({ each: true })
  @IsOptional()
  category: string[][];

  @IsOptional()
  categories: any; // This will be populated by DtoPipe from categories_in

  @IsOptional()
  name: string;

  @Transform(toMongoObjectId)
  @IsOptional()
  teacher: Types.ObjectId;

  @IsOptional()
  zoom_account: string;

  @Type(() => Number)
  @IsOptional()
  @IsArray()
  department_ids: number[];

  @Transform(toMongoObjectId)
  @IsOptional()
  check_user: string;

  @Transform(({ value }) => Number(value))
  @IsOptional()
  status: number;
}