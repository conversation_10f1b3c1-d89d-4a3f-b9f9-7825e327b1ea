import { Injectable } from "@nestjs/common";
import { Model } from "mongoose";
import { InjectModel } from "nestjs-typegoose";
import { EntityRepository } from "src/database/entity.repository";
import { CourseEvent, CourseEventDocument } from "./course-event.model";

@Injectable()
export class CourseEventsRepository extends EntityRepository<CourseEventDocument> {
  constructor(@InjectModel(CourseEvent) courseEventModel: Model<CourseEventDocument>) {
    super(courseEventModel)
  }
}