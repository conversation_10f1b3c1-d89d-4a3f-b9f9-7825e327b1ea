import { Body, Controller, Delete, Get, Param, Patch, Post, Query, UsePipes, ValidationPipe } from '@nestjs/common';
import { CourseEventsService } from './course-events.service';
import { DtoPipe } from 'src/pipes/dto.pipe';
import { FilterCourseEventsDto } from './dto/filter-course-events.dto';
import { AutoCreateCourseEventsDto } from './dto/auto-create-course-events.dto';
import { CreateCourseEventDto } from './dto/create-course-event.dto';
import { UpdateCourseEventDto } from './dto/update-course-event.dto';
import { MultiUpdateCourseEventDto } from './dto/multi-update-course-event.dto';
import { BulkCreateCourseEventsDto } from './dto/bulk-create-course-events.dto';

@Controller('course-events')
export class CourseEventsController {
  constructor(private courseEventsService: CourseEventsService) { }

  @Get()
  @UsePipes(DtoPipe)
  @UsePipes(new ValidationPipe({ transform: true }))
  getCourseEvents(@Query() dto: FilterCourseEventsDto) {
    return this.courseEventsService.getCourseEvents(dto)
  }

  @Get('/:id')
  getCourseEventById(@Param('id') id: string) {
    return this.courseEventsService.getCourseEventById(id);
  }

  @Post()
  @UsePipes(new ValidationPipe({ transform: true }))
  createCourseEvent(@Body() dto: CreateCourseEventDto) {
    return this.courseEventsService.createCourseEvent(dto);
  }

  @Post('/auto')
  @UsePipes(new ValidationPipe({ transform: true }))
  autoCreateCourseEvents(@Body() dto: AutoCreateCourseEventsDto) {
    return this.courseEventsService.autoCreateCourseEvents(dto);
  }

  @Post('/bulk')
  @UsePipes(new ValidationPipe({ transform: true }))
  bulkCreateCourseEvents(@Body() dto: BulkCreateCourseEventsDto) {
    return this.courseEventsService.bulkCreateCourseEvents(dto);
  }

  @Post('/multi-update')
  @UsePipes(new ValidationPipe({ transform: true }))
  multiUpdateCourseEvents(@Body() dto: MultiUpdateCourseEventDto) {
    return this.courseEventsService.multiUpdateCourseEvents(dto);
  }

  @Post('/finish/:id')
  @UsePipes(new ValidationPipe({ transform: true }))
  finishCourseEvent(@Param('id') id: string) {
    return this.courseEventsService.finishCourseEvent(id);
  }

  @Patch('/:id')
  @UsePipes(new ValidationPipe({ transform: true }))
  updateCourseEvent(@Param('id') id: string, @Body() dto: UpdateCourseEventDto) {
    return this.courseEventsService.updateCourseEvent(id, dto);
  }

  @Patch('/:id/toggle-notification')
  toggleCourseEventNotification(@Param('id') id: string) {
    return this.courseEventsService.toggleCourseEventNotification(id);
  }

  @Delete('/:id')
  deleteCourseEvent(@Param('id') id: string) {
    return this.courseEventsService.deleteCourseEvent(id);
  }
}
