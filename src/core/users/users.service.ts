import {
  ConflictException,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { DocumentType, ReturnModelType } from '@typegoose/typegoose';
import { InjectModel } from 'nestjs-typegoose';
import { CreateUserDto } from './dto/create-user.dto';
import { User } from './user.model';
import * as bcrypt from 'bcrypt';
import { UpdateUserDto } from './dto/update-user.dto';
import { LoginDto } from './dto/login.dto';
import { FilterUserDto } from './dto/filter-user.dto';
import { Pagination } from 'src/share/service';
import { ResetPasswordDto } from './dto/reset-password.dto';

@Injectable()
export class UsersService {
  constructor(
    @InjectModel(User)
    private readonly userModel: ReturnModelType<typeof User>,
  ) { }

  private readonly logger = new Logger(UsersService.name);

  async getUserById(id: string): Promise<DocumentType<User> | any> {
    try {
      const user = await this.userModel.findById(id);
      if (!user) {
        return {
          message: 'アカウントが存在しません。',
        };
      }
      return user;
    } catch (error) {
      this.logger.error(error.message);
      throw new InternalServerErrorException();
    }
  }

  async getUsers(filterUserDto: FilterUserDto): Promise<any> {
    const {
      perPage,
      page,
      sortField,
      sortOrder,
      full_name_cn_like: full_name_cn,
      ...filter
    } = filterUserDto;
    const matchFn = (pipeline: any) => {
      pipeline.addFields({
        full_name_cn: { $concat: ['$last_name_cn', '$first_name_cn'] },
      });
      if (full_name_cn) {
        pipeline.match({
          full_name_cn: {
            $regex: full_name_cn,
            $options: 'i',
          },
        });
      }
      return pipeline;
    };
    const { totalCount, data: users } = await Pagination(
      this.userModel,
      {
        perPage,
        page,
        sortField,
        sortOrder,
        ...filter,
      },
      matchFn,
    );
    return { totalCount, users };
  }

  async createUser(createUserDto: CreateUserDto): Promise<any> {
    try {
      const { password, ...dto } = createUserDto;
      const user = new this.userModel(dto);
      if (password) {
        const salt = await bcrypt.genSalt();
        const hashedPassword = await bcrypt.hash(password, salt);
        user.password = hashedPassword;
      }
      await user.save();
      return { _id: user._id };
    } catch (error) {
      if (error.code === 11000) {
        throw new ConflictException('User already exists.');
      } else {
        console.log(error);
        throw new InternalServerErrorException();
      }
    }
  }

  async updateUser(id: string, dto: UpdateUserDto): Promise<any> {
    try {
      const user = await this.getUserById(id);
      const { password, ...data } = dto;
      const result = await this.userModel.updateOne(
        { _id: id },
        { $set: data },
      );
      if (password) {
        const salt = await bcrypt.genSalt();
        const hashedPassword = await bcrypt.hash(password, salt);
        user.password = hashedPassword;
      }
      await user.save();
      return result.modifiedCount === 0
        ? { message: '更新失败' }
        : { message: '更新成功' };
    } catch (error) {
      this.logger.error(error.message);
      return { message: '更新失败' };
      // throw new InternalServerErrorException();
    }
  }

  async userLogin(dto: LoginDto): Promise<any> {
    try {
      const { email, password } = dto;
      const user = await this.userModel.findOne({ email });
      if (!user) {
        return {
          message: 'アカウントが存在しません。',
        };
      } else if (await bcrypt.compare(password, user.password)) {
        return user;
      } else {
        return {
          message: 'パスワードが正しくありません。',
        };
      }
    } catch (error) {
      this.logger.error(error.message);
      throw new InternalServerErrorException();
    }
  }

  async resetPassword(dto: ResetPasswordDto): Promise<any> {
    try {
      const { email, oldPassword, newPassword } = dto;
      const user = await this.userModel.findOne({ email });
      if (!user) {
        return {
          message: 'アカウントが存在しません。',
        };
      } else if (await bcrypt.compare(oldPassword, user.password)) {
        const salt = await bcrypt.genSalt();
        const hashedPassword = await bcrypt.hash(newPassword, salt);
        user.password = hashedPassword;
        await user.save();
        return {
          message: 'パスワードが更新されました。',
        };
      } else {
        return {
          message: 'パスワードが正しくありません。',
        };
      }
    } catch (error) {
      this.logger.error(error.message);
      throw new InternalServerErrorException();
    }
  }

  async deleteUser(id: string): Promise<any> {
    try {
      const result = await this.userModel.deleteOne({ _id: id }).exec();
      return result.deletedCount;
    } catch (error) {
      this.logger.error(error.message);
      throw new InternalServerErrorException();
    }
  }
}
