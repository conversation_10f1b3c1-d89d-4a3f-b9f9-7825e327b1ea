import { DocumentType, prop } from '@typegoose/typegoose';

// 确定1个
class BankAccount {
  @prop()
  public account_type: number; //⼯资收款账户类型: 1⽇本银⾏账户2中国银⾏账户3⽀付宝

  @prop()
  public bank_name: string; //银⾏名

  @prop()
  public bank_branch_name: string; //店名

  @prop()
  public account_no: string; //⼝座番号

  @prop()
  public holder_name: string; //⼝座名义

  @prop()
  public is_self: boolean; //收款账户是否为本⼈

  @prop()
  public payee_name: string; //收款⼈姓名

  @prop()
  public bank_account_images: string[]; //银⾏账户上图片

  @prop()
  public CN_ID_no: string; //收款⼈⼆代居⺠身份证号

  @prop()
  public CN_ID_images: string[]; //收款⼈⼆代居⺠身份证图片
}

class BaseSalary {
  @prop()
  public name: string[];

  @prop()
  public salary: number;
}

export class User {
  @prop({ required: true, unique: true })
  public email?: string;

  @prop({ required: true })
  public password: string;

  // 0未入职1正式2离职
  @prop({default: 0})
  public status: number;

  @prop()
  public can_courses: string;

  // courseEvent专用工资
  @prop({ type: () => [BaseSalary], _id: false, default: [] })
  public base_salary: BaseSalary[];

  // VIP时给
  @prop()
  public vip_salary: number;

  @prop()
  public last_name_cn: string;

  @prop()
  public last_name_en: string;

  @prop()
  public last_name_jp: string;

  @prop()
  public first_name_cn: string;

  @prop()
  public first_name_en: string;

  @prop()
  public first_name_jp: string;

  @prop()
  public birthday: string;

  @prop()
  public gender: number; //1:male 2:female

  @prop()
  public tel: string; //

  @prop()
  public address: string; //现居住地

  //根据身份证件类型选一个上传即可
  @prop()
  public ID_type: number; //身份证件类型: 1在留卡2⼆代居⺠身份证3护照

  @prop()
  public nationality: string; //国籍

  @prop()
  public ID_no: string; //证件号

  @prop()
  public ID_expiry_date: Date; //证件有效期限

  @prop()
  public ID_license: string; //在留资格

  @prop()
  public is_activity_permission: boolean; //资格外活动许可有无

  @prop()
  public is_dependents: boolean; //抚养有无

  @prop()
  public my_number: string; //个⼈番号

  @prop()
  public address_of_license: string; //证件上地址所在地

  @prop()
  public ID_images: string[]; //证件图片

  @prop()
  public graduate_university: string; //毕业院校名

  @prop()
  public graduate_date: string; //毕业时间

  @prop()
  public faculty: string; //专业

  @prop()
  public degree: string; //学位

  @prop({ type: () => BankAccount, _id: false, default: {} })
  public bank_account: BankAccount;

  // 身份数组:销售/教务/班主任/...
  @prop()
  public roles: string[];

  @prop({ default: 2 })
  public role: number; // 0管理员 1部门负责人 2普通老师
}

// 日本：4 00:00 ~ 7 24:00
// UTC: 3 15:00 ~ 7 15:00
