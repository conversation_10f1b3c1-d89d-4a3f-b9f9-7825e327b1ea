import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { CreateUserDto } from './dto/create-user.dto';
import { FilterUserDto } from './dto/filter-user.dto';
import { LoginDto } from './dto/login.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { UsersService } from './users.service';
import { DtoPipe } from 'src/pipes/dto.pipe';

@Controller('users')
export class UsersController {
  constructor(private usersService: UsersService) { }

  @Get()
  @UsePipes(DtoPipe)
  @UsePipes(new ValidationPipe({ transform: true }))
  getUsers(@Query() dto: FilterUserDto) {
    return this.usersService.getUsers(dto);
  }

  @Get('/:id')
  getUserById(@Param('id') id: string) {
    return this.usersService.getUserById(id);
  }

  @Post()
  @UsePipes(new ValidationPipe({ transform: true }))
  createUser(@Body() dto: CreateUserDto) {
    return this.usersService.createUser(dto);
  }

  @Patch('/:id')
  @UsePipes(new ValidationPipe({ transform: true }))
  updateUser(@Param('id') id: string, @Body() dto: UpdateUserDto) {
    return this.usersService.updateUser(id, dto);
  }

  @Delete('/:id')
  deleteUser(@Param('id') id: string) {
    return this.usersService.deleteUser(id);
  }

  @Post('/login')
  @UsePipes(new ValidationPipe({ transform: true }))
  userLogin(@Body() dto: LoginDto) {
    return this.usersService.userLogin(dto);
  }

  @Post('/reset-password')
  @UsePipes(new ValidationPipe({ transform: true }))
  resetPassword(@Body() dto: ResetPasswordDto) {
    return this.usersService.resetPassword(dto);
  }
}
