import { Transform } from 'class-transformer';
import { IsEmail, IsNumber, IsOptional, IsString } from 'class-validator';
import { ListDto } from 'src/share/dto';

export class FilterUserDto extends ListDto {
  @IsEmail()
  @IsOptional()
  email: string;

  @IsString()
  @IsOptional()
  full_name_cn_like: string;

  @Transform(({ value }) => ({ '$in': value.split(',').map((v: string) => +v)}))
  @IsOptional()
  role: any;

  @IsNumber()
  @Transform(({ value }) => Number(value))
  @IsOptional()
  status: number;
}
