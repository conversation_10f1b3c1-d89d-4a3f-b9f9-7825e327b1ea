import { Transform, Type } from 'class-transformer';
import { IsBoolean, IsEmail, IsNumber, IsOptional, IsString } from 'class-validator';

class BankAccount {
  @IsNumber()
  @Transform(({ value }) => Number(value))
  @IsOptional()
  account_type: number; //⼯资收款账户类型: 1⽇本银⾏账户2中国银⾏账户3⽀付宝

  @IsString()
  @IsOptional()
  bank_name: string; //银⾏名

  @IsString()
  @IsOptional()
  bank_branch_name: string; //店名

  @IsString()
  @IsOptional()
  account_no: string; //⼝座番号

  @IsString()
  @IsOptional()
  holder_name: string; //⼝座名义

  @IsBoolean()
  @IsOptional()
  is_self: boolean; //收款账户是否为本⼈

  @IsString()
  @IsOptional()
  payee_name: string; //收款⼈姓名

  @IsOptional()
  bank_account_images: string[]; //银⾏账户上图片

  @IsString()
  @IsOptional()
  CN_ID_no: string; //收款⼈⼆代居⺠身份证号

  @IsOptional()
  CN_ID_images: string[]; //收款⼈⼆代居⺠身份证图片
}

export class CreateUserDto {
  @IsString()
  @IsOptional()
  can_courses: string;
  
  @IsEmail()
  email: string;

  @IsString()
  password: string;

  @IsString()
  @IsOptional()
  last_name_cn: string;

  @IsString()
  @IsOptional()
  last_name_en: string;

  @IsString()
  @IsOptional()
  last_name_jp: string;

  @IsString()
  @IsOptional()
  first_name_cn: string;

  @IsString()
  @IsOptional()
  first_name_en: string;

  @IsString()
  @IsOptional()
  first_name_jp: string;

  @IsString()
  @IsOptional()
  birthday: string;

  @IsNumber()
  @Transform(({ value }) => Number(value))
  @IsOptional()
  gender: number; //1:male 2:female

  @IsString()
  @IsOptional()
  tel: string; //电话

  @IsString()
  @IsOptional()
  address: string; //现居住地

  @IsNumber()
  @Transform(({ value }) => Number(value))
  @IsOptional()
  ID_type: number; //身份证件类型: 1在留卡2⼆代居⺠身份证3护照

  @IsString()
  @IsOptional()
  nationality: string; //国籍

  @IsString()
  @IsOptional()
  ID_no: string; //证件号

  @IsString()
  @IsOptional()
  ID_expiry_date: Date; //证件有效期限

  @IsString()
  @IsOptional()
  ID_license: string; //在留资格
  
  @IsBoolean()
  @Transform(({ value }) => Boolean(value))
  @IsOptional()
  is_activity_permission: boolean; //资格外活动许可有无

  @IsBoolean()
  @Transform(({ value }) => Boolean(value))
  @IsOptional()
  is_dependents: boolean; //抚养有无

  @IsString()
  @IsOptional()
  my_number: string; //个⼈番号

  @IsString()
  @IsOptional()
  address_of_license: string; //证件上地址所在地

  @IsOptional()
  ID_images: string[]; //证件图片

  @IsString()
  @IsOptional()
  graduate_university: string; //毕业院校名

  @IsString()
  @IsOptional()
  graduate_date: string; //毕业时间

  @IsString()
  @IsOptional()
  faculty: string; //专业

  @IsString()
  @IsOptional()
  degree: string; //学位

  @Type(() => BankAccount)
  @IsOptional()
  bank_account: BankAccount;

  @IsNumber()
  @Transform(({ value }) => Number(value))
  @IsOptional()
  role: number;
}
