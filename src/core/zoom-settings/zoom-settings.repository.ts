import { Injectable } from "@nestjs/common";
import { Model } from "mongoose";
import { InjectModel } from "nestjs-typegoose";
import { EntityRepository } from "src/database/entity.repository";
import { ZoomSetting, ZoomSettingDocument } from "./zoom-setting.model";

@Injectable()
export class ZoomSettingsRepository extends EntityRepository<ZoomSettingDocument> {
  constructor(@InjectModel(ZoomSetting) zoomSettingModel: Model<ZoomSettingDocument>) {
    super(zoomSettingModel)
  }
}