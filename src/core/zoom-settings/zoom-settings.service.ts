import { Injectable } from '@nestjs/common';
import { ZoomSettingsRepository } from './zoom-settings.repository';
import { FilterZoomSettingsDto } from './dto/filter-zoom-settings.dto';
import { ZoomSettingDocument } from './zoom-setting.model';
import { CreateZoomSettingDto } from './dto/create-zoom-setting.dto';
import { UpdateZoomSettingDto } from './dto/update-zoom-setting.dto';

@Injectable()
export class ZoomSettingsService {
  constructor(
    private readonly zoomSettingsRepository: ZoomSettingsRepository
  ) { }

  async getZoomSettings(dto: FilterZoomSettingsDto): Promise<ZoomSettingDocument[]> {
    return this.zoomSettingsRepository.find(dto);
  }

  async createZoomSetting(dto: CreateZoomSettingDto): Promise<ZoomSettingDocument> {
    return this.zoomSettingsRepository.create(dto);
  }

  async updateZoomSetting(id: string, dto: UpdateZoomSettingDto): Promise<any> {
    return this.zoomSettingsRepository.update({ _id: id }, dto);
  }

  async deleteZoomSetting(id: string): Promise<any> {
    return this.zoomSettingsRepository.deleteMany({ _id: id });
  }
}
