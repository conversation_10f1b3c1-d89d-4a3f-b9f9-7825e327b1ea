import { Body, Controller, Delete, Get, Param, Patch, Post, Query, UsePipes, ValidationPipe } from '@nestjs/common';
import { ZoomSettingsService } from './zoom-settings.service';
import { DtoPipe } from 'src/pipes/dto.pipe';
import { FilterZoomSettingsDto } from './dto/filter-zoom-settings.dto';
import { CreateZoomSettingDto } from './dto/create-zoom-setting.dto';
import { UpdateZoomSettingDto } from './dto/update-zoom-setting.dto';

@Controller('zoom-settings')
export class ZoomSettingsController {
  constructor(
    private zoomSettingsService: ZoomSettingsService
  ) { }

  @Get()
  @UsePipes(DtoPipe)
  @UsePipes(new ValidationPipe({ transform: true }))
  getStudents(@Query() dto: FilterZoomSettingsDto) {
    return this.zoomSettingsService.getZoomSettings(dto);
  }

  @Post()
  @UsePipes(new ValidationPipe({ transform: true }))
  createStudent(@Body() dto: CreateZoomSettingDto) {
    return this.zoomSettingsService.createZoomSetting(dto);
  }

  @Patch('/:id')
  @UsePipes(new ValidationPipe({ transform: true }))
  updateStudent(@Param('id') id: string, @Body() dto: UpdateZoomSettingDto) {
    return this.zoomSettingsService.updateZoomSetting(id, dto);
  }

  @Delete('/:id')
  deleteUser(@Param('id') id: string) {
    return this.zoomSettingsService.deleteZoomSetting(id);
  }
}
