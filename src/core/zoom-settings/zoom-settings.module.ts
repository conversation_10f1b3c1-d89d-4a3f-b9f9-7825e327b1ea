import { Module } from '@nestjs/common';
import { ZoomSettingsService } from './zoom-settings.service';
import { ZoomSettingsController } from './zoom-settings.controller';
import { TypegooseModule } from 'nestjs-typegoose';
import { ZoomSetting } from './zoom-setting.model';
import { ZoomSettingsRepository } from './zoom-settings.repository';

@Module({
  imports: [
    TypegooseModule.forFeature([{
      typegooseClass: ZoomSetting
    }])
  ],
  providers: [ZoomSettingsService, ZoomSettingsRepository],
  controllers: [ZoomSettingsController]
})
export class ZoomSettingsModule { }
