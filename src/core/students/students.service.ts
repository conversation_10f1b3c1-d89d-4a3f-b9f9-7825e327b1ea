import { Injectable } from '@nestjs/common';
import { ReturnModelType } from '@typegoose/typegoose';
import { InjectModel } from 'nestjs-typegoose';
import { AddCourseDto } from './dto/add-course.dto';
import { CreateStudentDto } from './dto/create-student.dto';
import { DeleteCourseDto } from './dto/delete-course.dto';
import { filterStudentDto } from './dto/filter-student.dto';
import { UpdateCourseDto } from './dto/update-course.dto';
import { UpdateStudentDto } from './dto/update-student.dto';
import { Student } from './student.model';
import { StudentsRepository } from './students.repository';
import { AddPaymentDto } from './dto/add-payment.dto';
import { UpdatePaymentDto } from './dto/update-payment.dto';
import { calculateCourseExpiredDate } from '../../share/course-utils';
import { DeletePaymentDto } from './dto/delete-payment.dto';
import { AddFollowHistoryDto } from './dto/add-follow-history.dto';
import { UpdateFollowHistoryDto } from './dto/update-follow-history.dto';
import { DeleteFollowHistoryDto } from './dto/delete-follow-history.dto';
import { ArraysEqual } from 'src/share/service';
import { User } from '../users/user.model';
import * as dayjs from 'dayjs';

@Injectable()
export class StudentsService {
  constructor(
    @InjectModel(Student)
    private readonly studentModel: ReturnModelType<typeof Student>,
    @InjectModel(User)
    private readonly userModel: ReturnModelType<typeof User>,

    private readonly studentsRepository: StudentsRepository,
  ) {}
  private departmentEnum = {
    电子电气: '01',
    机械: '02',
    情报: '03',
    土木: '04',
    生农医药: '05',
    数学物理: '06',
    化学材料: '07',
    经营工: '08',
    其他: '11',
    学部: '00',
  };

  async getStudentById(id: string): Promise<Student> {
    return this.studentsRepository.findOne({ _id: id });
  }

  async getStudents(filterDto: filterStudentDto): Promise<any> {
    const { teacher, ...filter } = filterDto;
    const signStatusIn = filter.sign_status;
    const onStatusIn = filter.on_status;
    if (teacher) {
      const findedTeacher = await this.userModel.findById(teacher);
      const roles = findedTeacher.roles;
      if (signStatusIn) {
        const signStatus = Object.values(signStatusIn)[0];
        if (ArraysEqual(signStatus, [1])) {
          // 销售待处理
          if (!roles.includes('销售管理员')) {
            filter.sale_teacher1 = teacher;
          }
        } else if (ArraysEqual(signStatus, [2, 3])) {
          // 销售管理表
          if (!roles.includes('销售管理员')) {
            filter.sale_teacher1 = teacher;
          }
        } else if (ArraysEqual(signStatus, [2])) {
          // 学生待处理
        } else if (ArraysEqual(signStatus, [3])) {
          // 学生管理表
          // if (!roles.includes('教务管理员')) {
          //   // 仅付款30天以内的学生
          //   (filter as any).payment_histories = {
          //     $elemMatch: {
          //       'date': { $gte: dayjs().subtract(1, 'month').toDate() }
          //     }
          //     // $arrayElemAt: ['$payment_histories', 0]
          //   }
          // }
        }
      } else if (onStatusIn) {
        const onStatus = Object.values(onStatusIn)[0];
        if (
          ArraysEqual(onStatus, [2, 3, 4, 7]) ||
          ArraysEqual(onStatus, [5, 6])
        ) {
          let studentTypes = [];
          if (
            roles.includes('学部班主任管理员') ||
            roles.includes('学部班主任')
          ) {
            studentTypes = [...studentTypes, 1];
          }
          if (
            roles.includes('大学院班主任管理员') ||
            roles.includes('大学院班主任')
          ) {
            studentTypes = [...studentTypes, 2];
          }
          if (
            !roles.includes('学部班主任管理员') &&
            !roles.includes('大学院班主任管理员')
          ) {
            filter.current_follow_teacher = teacher;
          }
          (filter as any).student_type = {
            $in: studentTypes,
          };
        }
        // else if (ArraysEqual(onStatus, [5, 6])) {
        //   // 班主任过去学生
        //   if (!roles.includes('班主任管理员')) {
        //     filter.current_follow_teacher = teacher;
        //   }
        // }
      }
    }
    return this.studentsRepository.find(filter);
  }

  async createStudent(dto: CreateStudentDto): Promise<any> {
    const firstDay = new Date(new Date().getFullYear(), 0);
    const lastStudent = await this.studentModel
      .findOne({ createdAt: { $gte: firstDay } })
      .sort({ createdAt: -1 });
    if (!lastStudent) {
      dto.student_no = '0000';
    } else {
      dto.student_no = String(+lastStudent.student_no + 1).padStart(4, '0');
    }

    // Calculate course_expired_date if courses are provided
    if (dto.courses && dto.courses.length > 0) {
      dto.course_expired_date = calculateCourseExpiredDate(dto.courses);
    } else {
      dto.course_expired_date = null;
    }

    return this.studentsRepository.create(dto);
  }

  async updateStudent(id: string, dto: UpdateStudentDto) {
    return this.studentsRepository.update({ _id: id }, dto);
  }

  async deleteStudent(id: string): Promise<any> {
    return this.studentsRepository.deleteMany({ _id: id });
  }

  async addCourseToStudent(id: string, dto: AddCourseDto): Promise<any> {
    try {
      // Add the course to the student
      const result = await this.studentModel.updateOne(
        { _id: id },
        { $push: { courses: dto } },
      );
      if (result.modifiedCount === 0) {
        return { message: '添加失败' };
      }

      // Recalculate and update course_expired_date
      const student = await this.studentModel.findById(id);
      if (student) {
        const courseExpiredDate = calculateCourseExpiredDate(student.courses);
        await this.studentModel.updateOne(
          { _id: id },
          { $set: { course_expired_date: courseExpiredDate } }
        );
      }

      return { _id: id };
    } catch (error) {
      console.log(error);
      return { message: '添加失败' };
    }
  }

  async updateCourseFromStudent(dto: UpdateCourseDto): Promise<any> {
    try {
      const { student_id, course_id, course } = dto;
      const student = await this.studentModel.findById(student_id);
      const findCourse = student.courses.find((one) => one.id === course_id);
      if (findCourse) {
        for (const [key, value] of Object.entries(course)) {
          findCourse[key] = value;
        }
        student.markModified('courses');

        // Recalculate course_expired_date
        const courseExpiredDate = calculateCourseExpiredDate(student.courses);
        student.course_expired_date = courseExpiredDate;

        await student.save();
      }
      return { _id: student_id };
    } catch (error) {
      return { message: '更新失败' };
    }
  }

  async deleteCourseFromStudent(dto: DeleteCourseDto): Promise<any> {
    try {
      const { student_id, course_id } = dto;

      // Remove the course
      await this.studentModel.updateOne(
        { _id: student_id },
        { $pull: { courses: { _id: course_id } } },
      );

      // Recalculate and update course_expired_date
      const student = await this.studentModel.findById(student_id);
      if (student) {
        const courseExpiredDate = calculateCourseExpiredDate(student.courses);
        await this.studentModel.updateOne(
          { _id: student_id },
          { $set: { course_expired_date: courseExpiredDate } }
        );
      }

      return student_id;
    } catch (error) {
      return { message: '删除失败' };
    }
  }

  async addPaymentToStudent(id: string, dto: AddPaymentDto): Promise<any> {
    try {
      const result = await this.studentModel.updateOne(
        { _id: id },
        { $push: { payment_histories: dto } },
      );
      if (result.modifiedCount === 0) {
        return { message: '添加失败' };
      }
      return { _id: id };
    } catch (error) {
      console.log(error);
      return { message: '添加失败' };
    }
  }

  async updatePaymentFromStudent(dto: UpdatePaymentDto): Promise<any> {
    try {
      const { student_id, payment_id, payment } = dto;
      const student = await this.studentModel.findById(student_id);
      const findPayment = student.payment_histories.find(
        (one) => one.id === payment_id,
      );
      if (findPayment) {
        for (const [key, value] of Object.entries(payment)) {
          findPayment[key] = value;
        }
        student.markModified('payment_histories');
        await student.save();
      }
      return { _id: student_id };
    } catch (error) {
      return { message: '更新失败' };
    }
  }

  async deletePaymentFromStudent(dto: DeletePaymentDto): Promise<any> {
    try {
      const { student_id, payment_id } = dto;
      await this.studentModel.updateOne(
        { _id: student_id },
        { $pull: { payment_histories: { _id: payment_id } } },
      );
      return student_id;
    } catch (error) {
      return { message: '删除失败' };
    }
  }

  async addFollowHistoryToStudent(
    id: string,
    dto: AddFollowHistoryDto,
  ): Promise<any> {
    try {
      const result = await this.studentModel.updateOne(
        { _id: id },
        { $push: { follow_histories: dto } },
      );
      if (result.modifiedCount === 0) {
        return { message: '添加失败' };
      }
      return { _id: id };
    } catch (error) {
      console.log(error);
      return { message: '添加失败' };
    }
  }

  async updateFollowHistoryFromStudent(
    dto: UpdateFollowHistoryDto,
  ): Promise<any> {
    try {
      const { student_id, follow_history_id, follow_history } = dto;
      const student = await this.studentModel.findById(student_id);
      const findFollowHistory = student.follow_histories.find(
        (one) => one.id === follow_history_id,
      );
      if (findFollowHistory) {
        for (const [key, value] of Object.entries(follow_history)) {
          findFollowHistory[key] = value;
        }
        student.markModified('follow_histories');
        await student.save();
      }
      return { _id: student_id };
    } catch (error) {
      return { message: '更新失败' };
    }
  }

  async deleteFollowHistoryFromStudent(
    dto: DeleteFollowHistoryDto,
  ): Promise<any> {
    try {
      const { student_id, follow_history_id } = dto;
      await this.studentModel.updateOne(
        { _id: student_id },
        { $pull: { follow_histories: { _id: follow_history_id } } },
      );
      return student_id;
    } catch (error) {
      return { message: '删除失败' };
    }
  }

  async genContractNo(id: string): Promise<any> {
    try {
      const student = await this.studentsRepository.findOne({ _id: id });
      if (student.contract_no) return student.contract_no;
      const currentTime = new Date();
      const year = currentTime.getFullYear();
      const month = String(currentTime.getMonth() + 1).padStart(2, '0');
      const studentType = ['G', 'D', 'T'][student.student_type - 1];
      const departmentNo =
        this.departmentEnum[student?.courses[0]?.department_name];
      return {
        contractNo: `${year}${studentType}${month}${departmentNo}${student.student_no}`,
      };
    } catch (error) {
      throw error;
    }
  }

  async checkWechatExists(wechat: string): Promise<any> {
    try {
      const student = await this.studentModel.findOne({ wechat: wechat }).exec();
      return {
        exists: !!student,
        message: student ? '微信号已存在' : '微信号不存在'
      };
    } catch (error) {
      throw error;
    }
  }
}
