import { Injectable } from "@nestjs/common";
import { Model } from "mongoose";
import { InjectModel } from "nestjs-typegoose";
import { EntityRepository } from "src/database/entity.repository";
import { Student, StudentDocument } from "./student.model";

@Injectable()
export class StudentsRepository extends EntityRepository<StudentDocument> {
  constructor(@InjectModel(Student) studentModel: Model<StudentDocument>) {
    super(studentModel)
  }
}