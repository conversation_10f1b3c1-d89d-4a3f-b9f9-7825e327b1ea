import { Body, Controller, Delete, Get, Param, Patch, Post, Query, UsePipes, ValidationPipe } from '@nestjs/common';
import { DtoPipe } from 'src/pipes/dto.pipe';
import { AddCourseDto } from './dto/add-course.dto';
import { CreateStudentDto } from './dto/create-student.dto';
import { DeleteCourseDto } from './dto/delete-course.dto';
import { filterStudentDto } from './dto/filter-student.dto';
import { UpdateCourseDto } from './dto/update-course.dto';
import { UpdateStudentDto } from './dto/update-student.dto';
import { StudentsService } from './students.service';
import { AddPaymentDto } from './dto/add-payment.dto';
import { UpdatePaymentDto } from './dto/update-payment.dto';
import { DeletePaymentDto } from './dto/delete-payment.dto';
import { AddFollowHistoryDto } from './dto/add-follow-history.dto';
import { UpdateFollowHistoryDto } from './dto/update-follow-history.dto';
import { DeleteFollowHistoryDto } from './dto/delete-follow-history.dto';
import { CheckWechatDto } from './dto/check-wechat.dto';

@Controller('students')
export class StudentsController {
  constructor(private studentsService: StudentsService) { }

  @Get()
  @UsePipes(DtoPipe)
  @UsePipes(new ValidationPipe({ transform: true }))
  getStudents(@Query() dto: filterStudentDto) {
    return this.studentsService.getStudents(dto)
  }

  @Get('/check-wechat')
  @UsePipes(new ValidationPipe({ transform: true }))
  checkWechatExists(@Query() dto: CheckWechatDto) {
    return this.studentsService.checkWechatExists(dto.wechat);
  }

  @Get('/:id')
  getStudentById(@Param('id') id: string) {
    return this.studentsService.getStudentById(id);
  }

  @Post()
  @UsePipes(new ValidationPipe({ transform: true }))
  createStudent(@Body() dto: CreateStudentDto) {
    return this.studentsService.createStudent(dto);
  }

  @Post('/add-course/:id')
  @UsePipes(new ValidationPipe({ transform: true }))
  addCourseToStudent(@Param('id') id: string, @Body() dto: AddCourseDto) {
    return this.studentsService.addCourseToStudent(id, dto);
  }

  @Post('/update-course')
  @UsePipes(new ValidationPipe({ transform: true }))
  updateCourseFromStudent(@Body() dto: UpdateCourseDto) {
    return this.studentsService.updateCourseFromStudent(dto);
  }

  @Post('/delete-course')
  @UsePipes(new ValidationPipe({ transform: true }))
  deleteCourseFromStudent(@Body() dto: DeleteCourseDto) {
    return this.studentsService.deleteCourseFromStudent(dto);
  }

  @Post('/add-payment/:id')
  @UsePipes(new ValidationPipe({ transform: true }))
  addPaymentToStudent(@Param('id') id: string, @Body() dto: AddPaymentDto) {
    return this.studentsService.addPaymentToStudent(id, dto);
  }

  @Post('/update-payment')
  @UsePipes(new ValidationPipe({ transform: true }))
  updatePaymentFromStudent(@Body() dto: UpdatePaymentDto) {
    return this.studentsService.updatePaymentFromStudent(dto);
  }

  @Post('/delete-payment')
  @UsePipes(new ValidationPipe({ transform: true }))
  deletePaymentFromStudent(@Body() dto: DeletePaymentDto) {
    return this.studentsService.deletePaymentFromStudent(dto);
  }

  @Post('/add-follow-history/:id')
  @UsePipes(new ValidationPipe({ transform: true }))
  addFollowHistoryToStudent(@Param('id') id: string, @Body() dto: AddFollowHistoryDto) {
    return this.studentsService.addFollowHistoryToStudent(id, dto);
  }

  @Post('/update-follow-history')
  @UsePipes(new ValidationPipe({ transform: true }))
  updateFollowHistoryFromStudent(@Body() dto: UpdateFollowHistoryDto) {
    return this.studentsService.updateFollowHistoryFromStudent(dto);
  }

  @Post('/delete-follow-history')
  @UsePipes(new ValidationPipe({ transform: true }))
  deleteFollowHistoryFromStudent(@Body() dto: DeleteFollowHistoryDto) {
    return this.studentsService.deleteFollowHistoryFromStudent(dto);
  }

  @Post('/gen-contract-no/:id')
  @UsePipes(new ValidationPipe({ transform: true }))
  genContractNo(@Param('id') id: string,) {
    return this.studentsService.genContractNo(id);
  }

  @Patch('/:id')
  @UsePipes(new ValidationPipe({ transform: true }))
  updateStudent(@Param('id') id: string, @Body() dto: UpdateStudentDto) {
    return this.studentsService.updateStudent(id, dto);
  }

  @Delete('/:id')
  deleteUser(@Param('id') id: string) {
    return this.studentsService.deleteStudent(id);
  }
}
