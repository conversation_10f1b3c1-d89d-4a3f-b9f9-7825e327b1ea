import { Module } from '@nestjs/common';
import { StudentsService } from './students.service';
import { StudentsController } from './students.controller';
import { TypegooseModule } from 'nestjs-typegoose';
import { FollowHistory, Student } from './student.model';
import { StudentsRepository } from './students.repository';
import { User } from '../users/user.model';

@Module({
  imports: [
    TypegooseModule.forFeature([{
      typegooseClass: Student, schemaOptions: { timestamps: true }
    }, {
      typegooseClass: FollowHistory, schemaOptions: { timestamps: true }
    }, User])
  ],
  providers: [StudentsService, StudentsRepository],
  controllers: [StudentsController],
})
export class StudentsModule { }
