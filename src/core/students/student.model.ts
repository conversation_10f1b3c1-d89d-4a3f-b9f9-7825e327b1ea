import { DocumentType, ModelOptions, prop, Ref } from "@typegoose/typegoose";
import { Document } from "mongoose";
import { BaseModel } from "src/share/base.model";
import { User } from "../users/user.model";

export type StudentDocument = DocumentType<Student> & Document;

export class Student extends BaseModel {
  // 签约状态：1咨询中2签约中3已签约(暂定初始2，新增付款时变成3并且on_status=2)
  @prop({ default: 1 })
  public sign_status: number;

  // 在学状态：1签约中2在学中3冻结中4即将到期（怎么判定=>最晚的报名课程一个月开始）5已到期（怎么判定=>最晚的报名课程到期）6已退费 7 其他
  @prop()
  public on_status: number;

  // 学生属性 1学部2大学院3其他
  @prop({ default: 3 })
  public student_type: number;

  // 具体vip记录在新model里面，有个确认功能。[日付年月日date、開始時間string、終了時間string]
  // memo: vip跟申报记录进行关联，vip -> salary_record 单向同步。
  @prop({ default: false })
  public is_vip_course: boolean;
  
  @prop({ default: 0 })
  public vip_total_hours: number;

  @prop({ default: 0 })
  public vip_used_hours: number;

  // 第1销售第2销售
  // 只有1个班主任

  // ------------------------咨询阶段------------------------
  @prop({ required: true })
  public name: string;

  // 第1销售
  @prop({ ref: 'User' })
  public sale_teacher1?: Ref<User>;

  // 第2销售
  @prop({ ref: 'User' })
  public sale_teacher2?: Ref<User>;

  // 微信号
  @prop()
  public wechat: string;

  // 身份证号
  @prop()
  public CN_ID_no: string;

  // 在留卡号
  @prop()
  public JP_ID_no: string;

  // 毕业院校
  @prop()
  public grad_univ: string;

  // 毕业院校类别
  @prop()
  public grad_univ_category: string;

  @prop()
  public goal_univ: string;

  @prop()
  public goal_faculty: string;

  // 来日 1已定2未定
  @prop()
  public is_to_jp: boolean;

  // 来日时间
  @prop()
  public to_jp_date: Date;

  @prop()
  public is_lang_school: boolean;

  @prop()
  public lang_school: string;

  // 英语成绩
  @prop({ type: () => [Score], _id: false, default: [] })
  public en_scores: Score[];

  // 日语成绩
  @prop()
  @prop({ type: () => [Score], _id: false, default: [] })
  public ja_scores: Score[];

  @prop({ type: () => [Course], _id: true, default: [] })
  public courses: Course[];

  @prop()
  public is_gpa: boolean

  @prop()
  public gpa_score: string

  // 咨询备注
  @prop()
  public consult_note: string;

  // 考学规划
  @prop()
  public plan_contents: string;

  // ------------------------签约时阶段------------------------
  // 签约日期： 录入第一条付款记录，老师点击已签约时，自动生成。
  @prop()
  public sign_date: Date;

  // 综合课程有效期
  @prop()
  public sign_expired_date: Date;

  // 课程最晚到期日期（从所有课程的expired_date中取最大值）
  @prop()
  public course_expired_date: Date;

  // 编号顺序根据年份reset；第一次点击生成合同时确定，若无sign_date就生成sign_date(永久固定)，二次点击不会再更改了；新增student.contract_max_no（最后4位），倒序，然后+1来确定最后4位
  @prop()
  public contract_no: string;

  @prop()
  public student_no: string;

  // 契约条款备注
  @prop()
  public contract_note: string;

  @prop()
  public tel_jp: string;

  @prop()
  public tel_cn: string;

  @prop()
  public address: string;

  @prop()
  public emergency_name: string;

  @prop()
  public emergency_relationship: string;

  @prop()
  public emergency_tel: string;

  @prop()
  public emergency_wechat: string;

  // ------------------------付款阶段------------------------
  // 数组
  @prop({ type: () => [PaymentHistory], _id: true, default: [] })
  public payment_histories: PaymentHistory[];
  
  @prop()
  public is_paid: boolean;

  // ------------------------后期跟进阶段------------------------
  // 班主任
  @prop({ ref: 'User' })
  public current_follow_teacher?: Ref<User>;

  // 班主任转移怎么设计 => 当前只有1个
  // -------------------------------------------------------------
  @prop({ type: () => [FollowHistory], _id: true, default: [] })
  public follow_histories: FollowHistory[];
  // -------------------------------------------------------------

  // 合格院校
  @prop()
  public pass_univ: string;
}

@ModelOptions({ schemaOptions: { timestamps: true } })
class Course extends BaseModel {
  // 报名学院 => 选择式
  @prop()
  public department_name: string;

  // （例：情报基础，数学，英语）
  @prop()
  public course_name: string;

  @prop({ default: false })
  public is_ja_course: boolean;

  @prop()
  public ja_course_note: string;

  @prop({ default: false })
  public is_en_course: boolean;

  @prop()
  public en_course_note: string;

  @prop({ default: false })
  public is_math_course: boolean;

  @prop()
  public math_course_note: string;

  @prop()
  public vip_course: string;

  @prop()
  public original_price: number;

  @prop({ default: 0 })
  public off_price: number;

  // 人民币和日元怎么区分？=> 日元
  @prop()
  public final_price: number;

  @prop()
  public custom_source: string;

  @prop()
  public sign_date: Date;

  @prop()
  public expired_date: Date;

  @prop()
  public other_note: string;

  // 受理人
  @prop({ ref: 'User' })
  public accept_teacher?: Ref<User>;

  // 课程月份；加一个createdAt和updatedAt
  @prop()
  public course_month: number;
}

@ModelOptions({ schemaOptions: { timestamps: true } })
class PaymentHistory extends BaseModel {
  // TODO: 格式转换
  @prop()
  public type: number;

  @prop()
  public amount: number;

  @prop()
  public method: string;

  @prop()
  public date: Date;

  @prop()
  public note: string;
}

@ModelOptions({ schemaOptions: { timestamps: true } })
export class FollowHistory extends BaseModel {
  @prop()
  public category: string;

  @prop()
  public content: string;

  @prop({ default: [] })
  public files: string[];

  @prop({ ref: 'User' })
  public teacher?: Ref<User>;
}

class Score {
  @prop()
  public category: string;

  @prop()
  public score: string
}