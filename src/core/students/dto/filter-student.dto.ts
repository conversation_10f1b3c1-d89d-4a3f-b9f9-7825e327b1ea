import { Transform, Type } from "class-transformer";
import { IsBoolean, IsDate, IsN<PERSON>ber, IsOptional, IsString } from "class-validator";
import mongoose from "mongoose";
import { ListDto } from "src/share/dto";
import { toMongoObjectId } from "src/share/validator";

export class filterStudentDto extends ListDto {
  @Transform(toMongoObjectId)
  @IsOptional()
  teacher: mongoose.Types.ObjectId;

  @IsOptional()
  name: string;

  @IsOptional()
  @Transform(({ value }) => Number(value))
  student_type: number;

  @IsOptional()
  @Transform(({ value }) => Number(value))
  sign_status: number;

  @IsOptional()
  @Transform(({ value }) => Number(value))
  on_status: number;

  @Transform(toMongoObjectId)
  @IsOptional()
  current_follow_teacher: mongoose.Types.ObjectId;

  // TODO 同时搜索2个销售
  @Transform(toMongoObjectId)
  @IsOptional()
  sale_teacher1: mongoose.Types.ObjectId;

  @IsNumber()
  @Transform(({ value }) => Number(value))
  @IsOptional()
  univ_goal: number;

  @IsOptional()
  wechat: string;

  @IsString()
  @IsOptional()
  CN_ID_no: string;

  @IsString()
  @IsOptional()
  JP_ID_no: string;

  @IsString()
  @IsOptional()
  grad_univ: string;

  @IsString()
  @IsOptional()
  grad_univ_category: string;

  @IsOptional()
  goal_univ: string;

  @IsOptional()
  goal_faculty: string;

  @IsBoolean()
  @Type(() => Boolean)
  @IsOptional()
  is_to_jp: boolean;

  @IsOptional()
  to_jp_date: Object;

  @IsOptional()
  sign_date: Object;

  @IsOptional()
  sign_expired_date: Object;

  @IsOptional()
  createdAt: Object;

  @IsBoolean()
  @Type(() => Boolean)
  @IsOptional()
  is_lang_school: boolean;

  @IsOptional()
  tel_jp: string;

  @IsOptional()
  tel_cn: string;

  @IsBoolean()
  @Type(() => Boolean)
  @IsOptional()
  is_vip_course: boolean;

  @IsBoolean()
  @Type(() => Boolean)
  @IsOptional()
  is_paid: boolean;
}