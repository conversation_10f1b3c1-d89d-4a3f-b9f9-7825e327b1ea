import { Transform, Type } from "class-transformer";
import { IsBoolean, IsDate, IsNumber, IsOptional, IsString, ValidateNested } from "class-validator";
import mongoose from "mongoose";

class Score {
  @IsString()
  category: string;

  @IsString()
  score: string;
}

export class UpdateStudentDto {
  @IsString()
  @IsOptional()
  name: string;

  @IsNumber()
  @Transform(({ value }) => Number(value))
  @IsOptional()
  student_type: number;

  @Transform(({ value }) => new mongoose.Types.ObjectId(value))
  @IsOptional()
  sale_teacher1: string;

  @Transform(({ value }) => new mongoose.Types.ObjectId(value))
  @IsOptional()
  sale_teacher2: string;

  @IsString()
  @IsOptional()
  wechat: string;

  @IsString()
  @IsOptional()
  CN_ID_no: string;

  @IsString()
  @IsOptional()
  JP_ID_no: string;

  @IsString()
  @IsOptional()
  grad_univ: string;

  @IsString()
  @IsOptional()
  grad_faculty: string;

  @IsString()
  @IsOptional()
  goal_univ: string;

  @IsString()
  @IsOptional()
  grad_univ_category: string;

  @IsBoolean()
  @Type(() => Boolean)
  @IsOptional()
  is_to_jp: boolean;

  @Type(() => Date)
  @IsDate()
  @IsOptional()
  to_jp_date: Date;

  @IsBoolean()
  @Type(() => Boolean)
  @IsOptional()
  is_lang_school: boolean;

  @IsString()
  @IsOptional()
  lang_school: string;

  @ValidateNested({ each: true })
  @IsOptional()
  @Type(() => Score)
  en_scores: Score[];

  @ValidateNested({ each: true })
  @IsOptional()
  @Type(() => Score)
  ja_scores: Score[];

  @IsBoolean()
  @Type(() => Boolean)
  @IsOptional()
  is_gpa: boolean;

  @IsString()
  @IsOptional()
  gpa_score: string;

  @IsString()
  @IsOptional()
  consult_note: string;

  @IsString()
  @IsOptional()
  plan_contents: string;

  @Type(() => Date)
  @IsDate()
  @IsOptional()
  sign_expired_date: Date;

  @Type(() => Date)
  @IsDate()
  @IsOptional()
  course_expired_date: Date;
  
  @IsString()
  @IsOptional()
  tel_jp: string;

  @IsString()
  @IsOptional()
  tel_cn: string;

  @IsString()
  @IsOptional()
  address: string;

  @IsString()
  @IsOptional()
  emergency_name: string;

  @IsString()
  @IsOptional()
  emergency_relationship: string;

  @IsString()
  @IsOptional()
  emergency_tel: string;

  @IsString()
  @IsOptional()
  emergency_wechat: string;

  @IsString()
  @IsOptional()
  pass_univ: string;

  @IsBoolean()
  @Type(() => Boolean)
  @IsOptional()
  is_vip_course: boolean;

  @IsNumber()
  @Transform(({ value }) => Number(value))
  @IsOptional()
  vip_total_hours: number;

  @IsNumber()
  @Transform(({ value }) => Number(value))
  @IsOptional()
  vip_used_hours: number;
  
  @IsBoolean()
  @Type(() => Boolean)
  @IsOptional()
  is_paid: boolean;
}