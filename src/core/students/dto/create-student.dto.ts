import { Transform, Type } from "class-transformer";
import { IsBoolean, IsDate, IsN<PERSON>ber, IsOptional, IsString, ValidateNested } from "class-validator";
import { Types } from "mongoose";
import { toMongoObjectId } from "src/share/validator";

class Course {
  @IsString()
  department_name: string;

  @IsString()
  course_name: string;

  @IsBoolean()
  @Type(() => Boolean)
  @IsOptional()
  is_ja_course: boolean;

  @IsBoolean()
  @Type(() => Boolean)
  @IsOptional()
  is_en_course: boolean;

  @IsBoolean()
  @Type(() => Boolean)
  @IsOptional()
  is_math_course: boolean;

  @IsString()
  @IsOptional()
  vip_course: string;

  @IsNumber()
  @Transform(({ value }) => Number(value))
  original_price: number;

  @IsNumber()
  @Transform(({ value }) => Number(value))
  @IsOptional()
  off_price: number;

  @IsNumber()
  @Transform(({ value }) => Number(value))
  final_price: number;

  @IsString()
  @IsOptional()
  custom_source: string;

  @Type(() => Date)
  @IsDate()
  expired_date: Date;
  
  @Transform(toMongoObjectId)
  @IsOptional()
  accept_teacher: Types.ObjectId;

  @IsNumber()
  @Transform(({ value }) => Number(value))
  @IsOptional()
  course_month: number;
}

class Score {
  @IsString()
  category: string;

  @IsString()
  score: string;
}

export class CreateStudentDto {
  @IsString()
  name: string;

  @IsNumber()
  @Transform(({ value }) => Number(value))
  @IsOptional()
  student_type: number;

  @Transform(toMongoObjectId)
  @IsOptional()
  sale_teacher1: Types.ObjectId;

  @IsString()
  @IsOptional()
  wechat: string;

  @IsString()
  @IsOptional()
  CN_ID_no: string;

  @IsString()
  @IsOptional()
  JP_ID_no: string;

  @IsString()
  @IsOptional()
  grad_univ: string;

  @IsString()
  @IsOptional()
  grad_faculty: string;

  @IsString()
  @IsOptional()
  goal_univ: string;

  @IsString()
  @IsOptional()
  grad_univ_category: string;

  @IsBoolean()
  @Type(() => Boolean)
  @IsOptional()
  is_to_jp: boolean;

  @Type(() => Date)
  @IsDate()
  @IsOptional()
  to_jp_date: Date;

  @IsBoolean()
  @Type(() => Boolean)
  @IsOptional()
  is_lang_school: boolean;

  @IsString()
  @IsOptional()
  lang_school: string;

  @ValidateNested({ each: true })
  @IsOptional()
  @Type(() => Score)
  en_scores: Score[];

  @ValidateNested({ each: true })
  @IsOptional()
  @Type(() => Score)
  ja_scores: Score[];

  @IsBoolean()
  @Type(() => Boolean)
  @IsOptional()
  is_gpa: boolean;

  @IsString()
  @IsOptional()
  gpa_score: string;

  @IsString()
  @IsOptional()
  consult_note: string;

  @IsString()
  @IsOptional()
  apply_faculty: string;

  @IsString()
  @IsOptional()
  plan_contents: string;

  @Type(() => Date)
  @IsDate()
  @IsOptional()
  sign_expired_date: Date;

  @Type(() => Date)
  @IsDate()
  @IsOptional()
  course_expired_date: Date;

  @IsString()
  @IsOptional()
  tel_jp: string;

  @IsString()
  @IsOptional()
  tel_cn: string;

  @IsString()
  @IsOptional()
  address: string;

  @IsString()
  @IsOptional()
  emergency_name: string;

  @IsString()
  @IsOptional()
  emergency_relationship: string;

  @IsString()
  @IsOptional()
  emergency_tel: string;

  @IsString()
  @IsOptional()
  emergency_wechat: string;

  @ValidateNested({ each: true })
  @IsOptional()
  @Type(() => Course)
  courses: Course[];

  @IsString()
  @IsOptional()
  pass_univ: string;

  @IsString()
  @IsOptional()
  student_no: string;
}