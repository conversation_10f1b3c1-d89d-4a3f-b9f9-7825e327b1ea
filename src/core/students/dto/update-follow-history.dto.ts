import { Type } from "class-transformer";
import { IsOptional, IsString } from "class-validator";
import { AddPaymentDto } from "./add-payment.dto";
import { AddFollowHistoryDto } from "./add-follow-history.dto";

export class UpdateFollowHistoryDto {
  @IsString()
  @IsOptional()
  student_id: string;

  @IsString()
  @IsOptional()
  follow_history_id: string;

  @IsOptional()
  @Type(() => AddFollowHistoryDto)
  follow_history: AddFollowHistoryDto;
}