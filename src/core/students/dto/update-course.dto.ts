import { Transform, Type } from "class-transformer";
import { IsOptional, IsString } from "class-validator";
import { Types } from "mongoose";
import { toMongoObjectId } from "src/share/validator";
import { AddCourseDto } from "./add-course.dto";

export class UpdateCourseDto {
  @IsString()
  @IsOptional()
  student_id: string;

  @IsString()
  @IsOptional()
  course_id: string;

  @IsOptional()
  @Type(() => AddCourseDto)
  course: AddCourseDto;
}