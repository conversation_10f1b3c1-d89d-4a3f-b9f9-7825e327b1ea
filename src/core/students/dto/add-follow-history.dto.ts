import { Transform } from "class-transformer";
import { IsOptional, IsString } from "class-validator";
import { Types } from "mongoose";
import { toMongoObjectId } from "src/share/validator";

export class AddFollowHistoryDto {
  @IsString()
  @IsOptional()
  category: string;

  @IsString()
  @IsOptional()
  content: string;

  @IsOptional()
  files: string[];

  @Transform(toMongoObjectId)
  @IsOptional()
  teacher: Types.ObjectId;
}