import { Transform, Type } from "class-transformer";
import { IsBoolean, IsDate, IsN<PERSON>ber, IsOptional, IsString } from "class-validator";
import { Types } from "mongoose";
import { toMongoObjectId } from "src/share/validator";

export class AddCourseDto {
  @IsString()
  @IsOptional()
  department_name: string;

  @IsString()
  @IsOptional()
  course_name: string;

  @IsBoolean()
  @Type(() => Boolean)
  @IsOptional()
  is_ja_course: boolean;

  @IsBoolean()
  @Type(() => Boolean)
  @IsOptional()
  is_en_course: boolean;

  @IsBoolean()
  @Type(() => Boolean)
  @IsOptional()
  is_math_course: boolean;

  @IsString()
  @IsOptional()
  vip_course: string;

  @IsNumber()
  @Transform(({ value }) => Number(value))
  @IsOptional()
  original_price: number;

  @IsNumber()
  @Transform(({ value }) => Number(value))
  @IsOptional()
  off_price: number;

  @IsNumber()
  @Transform(({ value }) => Number(value))
  @IsOptional()
  final_price: number;

  @IsString()
  @IsOptional()
  custom_source: string;

  @Type(() => Date)
  @IsDate()
  @IsOptional()
  expired_date: Date;

  @Type(() => Date)
  @IsDate()
  @IsOptional()
  sign_date: Date;

  @Transform(toMongoObjectId)
  @IsOptional()
  accept_teacher: Types.ObjectId;

  @IsNumber()
  @Transform(({ value }) => Number(value))
  @IsOptional()
  course_month: number;
}