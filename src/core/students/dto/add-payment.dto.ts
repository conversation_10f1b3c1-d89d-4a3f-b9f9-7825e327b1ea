import { Transform, Type } from "class-transformer";
import { IsBoolean, IsDate, IsNumber, IsOptional, IsString } from "class-validator";

export class AddPaymentDto {
  @IsNumber()
  @Transform(({ value }) => Number(value))
  @IsOptional()
  type: number;

  @IsNumber()
  @Transform(({ value }) => Number(value))
  @IsOptional()
  amount: number;

  @IsString()
  @IsOptional()
  method: string;

  @Type(() => Date)
  @IsDate()
  @IsOptional()
  date: Date;

  @IsString()
  @IsOptional()
  note: string;
}