import { Transform, Type } from "class-transformer";
import { IsDate, IsOptional } from "class-validator";
import mongoose from "mongoose";

export class SummarySalaryDto {
  @Transform(({ value }) => new mongoose.Types.ObjectId(value))
  @IsOptional()
  user: string;

  @Transform(({ value }) => new mongoose.Types.ObjectId(value))
  check_user: string;

  @IsDate()
  @Type(() => Date)
  check_date_start: Date;

  @IsDate()
  @Type(() => Date)
  check_date_end: Date;
}