import { Body, Controller, Delete, Get, Param, Post, Query, UsePipes, ValidationPipe } from '@nestjs/common';
import { DtoPipe } from 'src/pipes/dto.pipe';
import { FilterSalaryMonthDto } from './dto/filter-salary-month.dto';
import { SummarySalaryDto } from './dto/summary-salary.dto';
import { SalaryMonthsService } from './salary-months.service';

@Controller('salary-months')
export class SalaryMonthsController {
  constructor(private salaryMonthsService: SalaryMonthsService) {}

  @Get()
  @UsePipes(new ValidationPipe({ transform: true }))
  @UsePipes(DtoPipe)
  getSalaryRecords(@Query() dto: FilterSalaryMonthDto) {
    return this.salaryMonthsService.getSalaryMonths(dto);
  }

  @Post('/summary')
  @UsePipes(new ValidationPipe({ transform: true }))
  summarySalary(@Body() dto: SummarySalaryDto) {
    return this.salaryMonthsService.summarySalary(dto);
  }

  @Delete('/:id')
  deleteSalaryMonth(@Param('id') id: string) {
    return this.salaryMonthsService.deleteSalaryMonth(id);
  }
}
