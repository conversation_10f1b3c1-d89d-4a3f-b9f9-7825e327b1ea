import { Modu<PERSON> } from '@nestjs/common';
import { TypegooseModule } from 'nestjs-typegoose';
import { SalaryRecord } from '../salary-records/salary-record.model';
import { User } from '../users/user.model';
import { SalaryMonth } from './salary-month.model';
import { SalaryMonthsController } from './salary-months.controller';
import { SalaryMonthsService } from './salary-months.service';

@Module({
  imports: [
    TypegooseModule.forFeature([
      { typegooseClass: SalaryMonth, schemaOptions: { timestamps: true } },
      SalaryRecord,
      User,
    ]),
  ],
  controllers: [SalaryMonthsController],
  providers: [SalaryMonthsService]
})
export class SalaryMonthsModule {}
