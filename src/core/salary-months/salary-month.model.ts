import { prop, Ref } from "@typegoose/typegoose";
import { User } from "../users/user.model";

export class SalaryMonth {
  @prop({ ref: 'User' })
  public user: Ref<User>;

  @prop()
  public full_name_cn: string;

  @prop()
  public account_type: number;

  @prop()
  public bank_name: string;

  @prop()
  public bank_branch_name: string;

  @prop()
  public account_no: string;

  @prop()
  public holder_name: string;

  @prop()
  public base_amount: number;

  @prop()
  public travel_fee: number;

  @prop()
  public total_amount: number;

  @prop({ type: () => [DepartmentAmount], _id: false, default: [] })
  public department_amount: DepartmentAmount[];

  @prop()
  public year: string;

  @prop()
  public month: string;

  @prop({ type: () => [CheckUserSalary], _id: false, default: [] })
  public check_user_salaries: CheckUserSalary[];
}

class CheckUserSalary {
  @prop({ ref: 'User' })
  public check_user: Ref<User>;

  @prop()
  public base_amount: number;

  @prop()
  public travel_fee: number;

  @prop()
  public total_amount: number;

  @prop({ type: () => [DepartmentAmount], _id: false, default: [] })
  public department_amount: DepartmentAmount[];
}

class DepartmentAmount {
  @prop()
  public department_ids: number[];

  @prop()
  public total_amount: number;
}