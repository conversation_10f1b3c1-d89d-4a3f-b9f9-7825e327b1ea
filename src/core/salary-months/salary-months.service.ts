import { Injectable, InternalServerErrorException, Logger } from '@nestjs/common';
import { ReturnModelType } from '@typegoose/typegoose';
import { InjectModel } from 'nestjs-typegoose';
import { SalaryRecord } from '../salary-records/salary-record.model';
import { User } from '../users/user.model';
import { SummarySalaryDto } from './dto/summary-salary.dto';
import { FilterSalaryMonthDto } from './dto/filter-salary-month.dto';
import { SalaryMonth } from './salary-month.model';
import { Pagination } from 'src/share/service';

@Injectable()
export class SalaryMonthsService {
  constructor(
    @InjectModel(SalaryMonth)
    private readonly salaryMonthModel: ReturnModelType<typeof SalaryMonth>,
    @InjectModel(SalaryRecord)
    private readonly salaryRecordModel: ReturnModelType<typeof SalaryRecord>,
    @InjectModel(User)
    private readonly userModel: ReturnModelType<typeof User>,
  ) { }

  private readonly logger = new Logger(SalaryMonthsService.name);

  async getSalaryMonths(dto: FilterSalaryMonthDto): Promise<any> {
    const {
      page = 0,
      perPage = 99999,
      sortField = 'createdAt',
      sortOrder = 1,
      check_user,
      ...filter
    } = dto;
    const checkUser = await this.userModel.findById(check_user);
    const pipeline = this.salaryMonthModel.aggregate();
    pipeline.match(filter);
    if (checkUser.role === 1) {
      pipeline.addFields({
        checkUserSalary: {
          $filter: {
            input: '$check_user_salaries',
            as: 'item',
            cond: { $eq: ['$$item.check_user', check_user] },
          },
        },
      });
      pipeline.match({ checkUserSalary: { $size: 1 } });
    }
    pipeline.lookup({
      from: 'users',
      localField: 'user',
      foreignField: '_id',
      as: 'user',
    })
    pipeline.unwind({
      path: '$user',
      preserveNullAndEmptyArrays: true,
    });
    pipeline.skip(page * perPage);
    pipeline.limit(perPage);

    pipeline.project({
      check_user_salaries: 0,
    });
    const salaryMonths = await pipeline.exec();
    const totalCount = salaryMonths.length;
    return { totalCount, salaryMonths };
  }

  async summarySalary(dto: SummarySalaryDto): Promise<any> {
    try {
      const { user, check_user, check_date_start, check_date_end } = dto;
      const year = check_date_start.getFullYear();
      const month = check_date_start.getMonth() + 1;
      const pipeline = this.salaryRecordModel.aggregate();
      const checkUser = await this.userModel.findById(check_user);
      if (checkUser.role === 1) {
        pipeline.match({ check_user });
      }
      if (user) {
        pipeline.match({ user });
      }
      pipeline.match({ check_date: { $gte: check_date_start } });
      pipeline.match({ check_date: { $lte: check_date_end } });
      pipeline.match({ apply_status: { $ne: 4 } });
      pipeline.group({
        _id: { user: '$user', department_ids: '$department_ids', apply_type: '$apply_type' },
        total_amount: { $sum: '$final_salary' },
        travel_fee: { $sum: '$travel_fee' },
        base_amount: { $sum: { $subtract: ['$final_salary', '$travel_fee'] } },
      });
      pipeline.group({
        _id: '$_id.user',
        total_amount: { $sum: '$total_amount' },
        travel_fee: { $sum: '$travel_fee' },
        base_amount: { $sum: '$base_amount' },
        // TODO: 3级分类，统计时间，字数，人数，定给
        department_amount: { $addToSet: { department_ids: '$_id.department_ids', apply_type: '$_id.apply_type', total_amount: '$total_amount' } }
      })
      const salaryRecords = await pipeline.exec();
      for (const s of salaryRecords) {
        const user = await this.userModel.findById(s._id);
        let salaryMonth = await this.salaryMonthModel.findOne({
          user,
          year,
          month,
        });
        if (!salaryMonth) {
          salaryMonth = new this.salaryMonthModel({
            user,
            full_name_cn: `${user.last_name_cn}${user.first_name_cn}`,
            account_type: user.bank_account.account_type,
            bank_name: user.bank_account.bank_name,
            bank_branch_name: user.bank_account.bank_branch_name,
            account_no: user.bank_account.account_no,
            holder_name: user.bank_account.holder_name,
            year,
            month,
          });
        }
        if (checkUser.role === 1) {
          salaryMonth.check_user_salaries = salaryMonth.check_user_salaries.filter(
            (c) => String(c.check_user) !== String(check_user),
          );
          salaryMonth.check_user_salaries.push({
            check_user: checkUser,
            base_amount: s.base_amount,
            travel_fee: s.travel_fee,
            total_amount: s.total_amount,
            department_amount: s.department_amount,
          });
        } else if (checkUser.role === 0) {
          salaryMonth.base_amount = s.base_amount;
          salaryMonth.travel_fee = s.travel_fee;
          salaryMonth.total_amount = s.total_amount;
          salaryMonth.department_amount = s.department_amount;
        }
        await salaryMonth.save();
      }
      return { message: 'success!' };
    } catch (error) {
      throw error;
    }
  }

  async deleteSalaryMonth(id: string): Promise<any> {
    try {
      const result = await this.salaryMonthModel.deleteOne({ _id: id }).exec();
      return result.deletedCount;
    } catch (error) {
      this.logger.error(error.message);
      throw new InternalServerErrorException();
    }
  }
}
