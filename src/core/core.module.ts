import { Module } from '@nestjs/common';
import { UsersModule } from './users/users.module';
import { SalaryRecordsModule } from './salary-records/salary-records.module';
import { SalaryMonthsModule } from './salary-months/salary-months.module';
import { StudentsModule } from './students/students.module';
import { VipCoursesModule } from './vip-courses/vip-courses.module';
import { CourseEventsModule } from './course-events/course-events.module';
import { ZoomSettingsModule } from './zoom-settings/zoom-settings.module';

@Module({
  imports: [UsersModule, SalaryRecordsModule, SalaryMonthsModule, StudentsModule, VipCoursesModule, CourseEventsModule, ZoomSettingsModule],
})
export class CoreModule {}
