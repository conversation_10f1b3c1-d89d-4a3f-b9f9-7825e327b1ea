import { Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { LoggingInterceptor } from './interceptors/logging.interceptor';

async function bootstrap() {
  const logger = new Logger('bootstrap');
  const app = await NestFactory.create(AppModule);
  const config = app.get(ConfigService);
  
  // Apply the logging interceptor globally
  app.useGlobalInterceptors(new LoggingInterceptor());
  
  app.enableCors();
  await app.listen(config.get('PORT'));
  logger.verbose(`Application listening on port ${config.get('PORT')}`);
}
bootstrap();
