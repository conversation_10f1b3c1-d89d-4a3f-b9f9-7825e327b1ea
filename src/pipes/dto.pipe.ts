import { Injectable, Logger, PipeTransform } from "@nestjs/common";
import * as dayjs from 'dayjs';

@Injectable()
export class DtoPipe implements PipeTransform {
  private readonly logger = new Logger(DtoPipe.name);

  transform(value: any) {
    this.logger.debug('DtoPipe running...');
    console.log('=== DtoPipe transform called ===');
    console.log('Input value:', JSON.stringify(value, null, 2));

    for (const key in value) {
      // 处理带_in后缀的number[]搜索
      if (key.slice(key.length - 3) === '_in') {
        console.log(`Processing _in parameter: ${key}`);
        console.log(`Original value: ${value[key]}`);

        const parsedValue = JSON.parse(value[key]);
        console.log('Parsed value:', parsedValue);

        value[key.slice(0, key.length - 3)] = {
          $in: parsedValue
        }
        console.log(`Created new key: ${key.slice(0, key.length - 3)}`);
        console.log('New value structure:', value[key.slice(0, key.length - 3)]);

        delete value[key];
        console.log(`Deleted original key: ${key}`);
      }

      // 处理带_lk后缀的模糊搜索
      else if (key.slice(key.length - 3) === '_lk') {
        value[key.slice(0, key.length - 3)] = {
          $regex: value[key],
          $options: 'i',
        }
        delete value[key];
      }

      // 处理带_start后缀的区间搜索
      else if (key.slice(key.length - 6) === '_start') {
        const dateKey = key.slice(0, key.length - 6);
        if (value.hasOwnProperty(dateKey)) {
          value[dateKey]['$gte'] = dateKey === 'work_date' ? new Date(value[key]) : dayjs(value[key]).subtract(9, 'hour').toDate();
        } else {
          value[dateKey] = {
            '$gte': new Date(value[key])
          }
        }
        delete value[key];
      }
      // 处理带_end后缀的区间搜索
      else if (key.slice(key.length - 4) === '_end') {
        const dateKey = key.slice(0, key.length - 4);
        if (value.hasOwnProperty(dateKey)) {
          value[dateKey]['$lte'] = dateKey === 'work_date' ? new Date(value[key]) : dayjs(value[key]).subtract(9, 'hour').toDate();

        } else {
          value[dateKey] = {
            '$lte': new Date(value[key])
          }
        }
        delete value[key];
      }

      if (key.slice(key.length - 4) === '_int') {
        value[key.slice(0, key.length - 4)] = Number(value[key]);
        delete value[key];
      } else if (key.slice(key.length - 5) === '_bool') {
        value[key.slice(0, key.length - 5)] = value[key] === 'true' ? true : false;
        delete value[key]
      }

    }

    console.log('=== DtoPipe transform complete ===');
    console.log('Final transformed value:', JSON.stringify(value, null, 2));
    return value;
  }
}