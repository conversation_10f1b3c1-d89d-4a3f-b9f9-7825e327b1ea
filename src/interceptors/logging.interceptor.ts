import { Injectable, NestInterceptor, ExecutionContext, CallH<PERSON>ler, Logger } from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  private logger = new Logger('HTTP');

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const req = context.switchToHttp().getRequest();
    const { method, url, query, body } = req;
    
    const now = Date.now();
    
    this.logger.log(
      `${method} ${url} - Query: ${JSON.stringify(query)} - Body: ${JSON.stringify(body)}`
    );

    return next
      .handle()
      .pipe(
        tap(() => {
          const response = context.switchToHttp().getResponse();
          const delay = Date.now() - now;
          this.logger.log(
            `${method} ${url} ${response.statusCode} - ${delay}ms`
          );
        }),
      );
  }
}