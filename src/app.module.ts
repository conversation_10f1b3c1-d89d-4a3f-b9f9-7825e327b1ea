import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { APP_PIPE } from '@nestjs/core';
import configuration from 'config/configuration';
import { TypegooseModule } from 'nestjs-typegoose';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { CoreModule } from './core/core.module';
import { DtoPipe } from './pipes/dto.pipe';
import * as mongoose from 'mongoose';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [configuration],
    }),
    TypegooseModule.forRootAsync({
      useFactory: async (config: ConfigService) => {
        mongoose.set('debug', true);
        
        return {
        uri: config.get('MONGO_URL'),
        useNewUrlParser: true,
        useUnifiedTopology: true,
      };
      },
      inject: [ConfigService],
    }),
    CoreModule,
  ],
  controllers: [AppController],
  providers: [AppService, 
    // RequestService,
    // {
    //   provide: APP_GUARD,
    //   useClass: AuthGuard
    // }, {
    //   provide: APP_INTERCEPTOR,
    //   scope: Scope.REQUEST,
    //   useClass: LoggingInterceptor,
    // },
    // {
    //   provide: APP_PIPE,
    //   useClass: DtoPipe,
    // },
    // {
    //   provide: APP_FILTER,
    //   useClass: HttpExceptionFilter,
    // }
  ],
})
export class AppModule { }
