/**
 * MongoDB Migration Script: Add course_expired_date field to Student documents
 * 
 * This script adds the course_expired_date field to existing student documents
 * and calculates the initial values based on the maximum expired_date from their courses.
 * 
 * The course_expired_date field represents the latest expiration date among all courses
 * associated with a student. If a student has no courses or no valid expired_date values,
 * the field will be set to null.
 * 
 * Usage:
 * 1. Via MongoDB shell: mongo your_database_name scripts/migrate-course-expired-date.js
 * 2. Via MongoDB Compass: Copy the migration logic below
 */

// MongoDB shell script version
function migrateCourseExpiredDate() {
  print("开始迁移学生课程到期日期字段...");
  
  const collection = db.students; // Adjust collection name if different
  
  // Find all student documents
  const cursor = collection.find({});
  
  let migratedCount = 0;
  let skippedCount = 0;
  let errorCount = 0;
  let totalFound = 0;
  
  cursor.forEach(function(doc) {
    totalFound++;
    
    try {
      // Check if course_expired_date field already exists
      if (doc.hasOwnProperty('course_expired_date')) {
        print("跳过已有course_expired_date字段的文档: " + doc._id);
        skippedCount++;
        return;
      }
      
      let maxExpiredDate = null;
      
      // Calculate maximum expired_date from courses
      if (doc.courses && Array.isArray(doc.courses) && doc.courses.length > 0) {
        const validDates = [];
        
        doc.courses.forEach(function(course) {
          if (course.expired_date && course.expired_date instanceof Date) {
            validDates.push(course.expired_date);
          }
        });
        
        if (validDates.length > 0) {
          // Find the maximum date
          maxExpiredDate = new Date(Math.max.apply(null, validDates.map(function(date) {
            return date.getTime();
          })));
        }
      }
      
      // Update the document with the new field
      const result = collection.updateOne(
        { _id: doc._id },
        { $set: { course_expired_date: maxExpiredDate } }
      );
      
      if (result.modifiedCount > 0) {
        const dateStr = maxExpiredDate ? maxExpiredDate.toISOString() : "null";
        print("迁移成功: " + doc._id + " | course_expired_date: " + dateStr + " | 课程数量: " + (doc.courses ? doc.courses.length : 0));
        migratedCount++;
      }
    } catch (error) {
      print("迁移失败: " + doc._id + " | 错误: " + error.message);
      errorCount++;
    }
  });
  
  const result = {
    message: "课程到期日期字段迁移完成",
    totalFound: totalFound,
    migrated: migratedCount,
    skipped: skippedCount,
    errors: errorCount
  };
  
  print("迁移结果: " + JSON.stringify(result, null, 2));
  return result;
}

// Rollback function to remove the course_expired_date field
function rollbackCourseExpiredDate() {
  print("开始回滚课程到期日期字段...");
  
  const collection = db.students;
  
  const result = collection.updateMany(
    { course_expired_date: { $exists: true } },
    { $unset: { course_expired_date: "" } }
  );
  
  const rollbackResult = {
    message: "课程到期日期字段回滚完成",
    modifiedCount: result.modifiedCount
  };
  
  print("回滚结果: " + JSON.stringify(rollbackResult, null, 2));
  return rollbackResult;
}

// Run the migration
migrateCourseExpiredDate();
