/**
 * TypeScript Migration Script for Student course_expired_date field
 * 
 * This script provides both forward and rollback migration capabilities
 * for adding the course_expired_date field to student documents.
 */

import { MongoClient, Db, Collection } from 'mongodb';

interface Course {
  expired_date?: Date | null;
  [key: string]: any;
}

interface Student {
  _id: any;
  courses?: Course[];
  course_expired_date?: Date | null;
  [key: string]: any;
}

interface MigrationResult {
  message: string;
  totalFound: number;
  migrated: number;
  skipped: number;
  errors: number;
  details?: string[];
}

class CourseExpiredDateMigration {
  private db: Db;
  private collection: Collection<Student>;

  constructor(db: Db) {
    this.db = db;
    this.collection = db.collection('students'); // Adjust collection name if needed
  }

  /**
   * Calculate the maximum expired_date from courses array
   */
  private calculateMaxExpiredDate(courses: Course[]): Date | null {
    if (!courses || courses.length === 0) {
      return null;
    }

    const validDates = courses
      .map(course => course.expired_date)
      .filter((date): date is Date => date != null && date instanceof Date)
      .filter(date => !isNaN(date.getTime()));

    if (validDates.length === 0) {
      return null;
    }

    return new Date(Math.max(...validDates.map(date => date.getTime())));
  }

  /**
   * Forward migration: Add course_expired_date field
   */
  async migrateForward(): Promise<MigrationResult> {
    console.log("开始迁移学生课程到期日期字段...");
    
    const cursor = this.collection.find({});
    
    let migratedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;
    let totalFound = 0;
    const details: string[] = [];

    for await (const doc of cursor) {
      totalFound++;
      
      try {
        // Check if course_expired_date field already exists
        if (doc.hasOwnProperty('course_expired_date')) {
          details.push(`跳过已有字段: ${doc._id}`);
          skippedCount++;
          continue;
        }

        const maxExpiredDate = this.calculateMaxExpiredDate(doc.courses || []);
        
        await this.collection.updateOne(
          { _id: doc._id },
          { $set: { course_expired_date: maxExpiredDate } }
        );
        
        const dateStr = maxExpiredDate ? maxExpiredDate.toISOString() : "null";
        details.push(`迁移成功: ${doc._id} | course_expired_date: ${dateStr} | 课程数量: ${doc.courses?.length || 0}`);
        migratedCount++;
      } catch (error) {
        details.push(`错误: ${doc._id} | ${error.message}`);
        errorCount++;
      }
    }

    const result: MigrationResult = {
      message: "课程到期日期字段迁移完成",
      totalFound,
      migrated: migratedCount,
      skipped: skippedCount,
      errors: errorCount,
      details
    };

    console.log("迁移结果:", JSON.stringify(result, null, 2));
    return result;
  }

  /**
   * Rollback migration: Remove course_expired_date field
   */
  async rollback(): Promise<MigrationResult> {
    console.log("开始回滚课程到期日期字段...");
    
    const result = await this.collection.updateMany(
      { course_expired_date: { $exists: true } },
      { $unset: { course_expired_date: "" } }
    );

    const rollbackResult: MigrationResult = {
      message: "课程到期日期字段回滚完成",
      totalFound: 0,
      migrated: result.modifiedCount,
      skipped: 0,
      errors: 0
    };

    console.log("回滚结果:", JSON.stringify(rollbackResult, null, 2));
    return rollbackResult;
  }

  /**
   * Validate migration by checking field existence and values
   */
  async validate(): Promise<{ hasField: number; nullValues: number; validValues: number }> {
    const hasFieldCount = await this.collection.countDocuments({ course_expired_date: { $exists: true } });
    const nullValuesCount = await this.collection.countDocuments({ course_expired_date: null });
    const validValuesCount = await this.collection.countDocuments({ 
      course_expired_date: { $exists: true, $ne: null } 
    });

    return {
      hasField: hasFieldCount,
      nullValues: nullValuesCount,
      validValues: validValuesCount
    };
  }
}

// Export for use in other modules
export { CourseExpiredDateMigration, MigrationResult };

// Example usage (uncomment to run directly)
/*
async function runMigration() {
  const client = new MongoClient('mongodb://localhost:27017');
  await client.connect();
  
  const db = client.db('your_database_name');
  const migration = new CourseExpiredDateMigration(db);
  
  try {
    // Run forward migration
    await migration.migrateForward();
    
    // Validate migration
    const validation = await migration.validate();
    console.log('验证结果:', validation);
  } finally {
    await client.close();
  }
}

// runMigration().catch(console.error);
*/
