/**
 * MongoDB Migration Script: Convert CourseEvent categories from string[] to string[][]
 * 
 * This script migrates existing course event data to support the new nested category structure.
 * Each existing category string becomes its own array within the category array.
 * 
 * Example transformation:
 * Before: category: ["数学", "数学Ⅰ", "正课"]
 * After:  category: [["数学", "数学Ⅰ", "正课"]]
 * 
 * Usage:
 * 1. Via API endpoint: POST /course-events/migrate-categories
 * 2. Direct MongoDB script: mongo your_database_name scripts/migrate-categories.js
 * 3. MongoDB Compass: Copy the migration logic below
 */

// MongoDB shell script version
function migrateCourseEventCategories() {
  print("开始迁移课程事件分类数据...");
  
  const collection = db.courseevents; // Adjust collection name if different
  
  // Find all documents with category field
  const cursor = collection.find({
    category: { $exists: true, $ne: null }
  });
  
  let migratedCount = 0;
  let skippedCount = 0;
  let errorCount = 0;
  let totalFound = 0;
  
  cursor.forEach(function(doc) {
    totalFound++;
    
    try {
      // Check if category is already in new format
      if (Array.isArray(doc.category) && doc.category.length > 0) {
        // Check if first element is an array (new format)
        if (Array.isArray(doc.category[0])) {
          print("跳过已迁移的文档: " + doc._id);
          skippedCount++;
          return;
        }
        
        // Convert from string[] to string[][]
        // Wrap the entire array as the first element of a new array
        const newCategory = [doc.category];
        
        // Update the document
        const result = collection.updateOne(
          { _id: doc._id },
          { $set: { category: newCategory } }
        );
        
        if (result.modifiedCount > 0) {
          print("迁移成功: " + doc._id + " | " + JSON.stringify(doc.category) + " -> " + JSON.stringify(newCategory));
          migratedCount++;
        }
      } else {
        // Handle null or empty categories
        collection.updateOne(
          { _id: doc._id },
          { $set: { category: [] } }
        );
        print("设置空分类: " + doc._id);
        migratedCount++;
      }
    } catch (error) {
      print("迁移失败: " + doc._id + " | 错误: " + error.message);
      errorCount++;
    }
  });
  
  const result = {
    message: "分类数据迁移完成",
    totalFound: totalFound,
    migrated: migratedCount,
    skipped: skippedCount,
    errors: errorCount
  };
  
  print("迁移结果: " + JSON.stringify(result, null, 2));
  return result;
}

// Run the migration
migrateCourseEventCategories();
