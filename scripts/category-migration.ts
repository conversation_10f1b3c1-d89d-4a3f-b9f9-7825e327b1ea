/**
 * TypeScript Migration Script for CourseEvent Categories
 * 
 * This script provides both forward and rollback migration capabilities
 * for the category field transformation from string[] to string[][].
 */

import { MongoClient, Db, Collection } from 'mongodb';

interface CourseEvent {
  _id: any;
  category: string[] | string[][];
  [key: string]: any;
}

interface MigrationResult {
  message: string;
  totalFound: number;
  migrated: number;
  skipped: number;
  errors: number;
  details?: string[];
}

class CategoryMigration {
  private db: Db;
  private collection: Collection<CourseEvent>;

  constructor(db: Db) {
    this.db = db;
    this.collection = db.collection('courseevents'); // Adjust collection name if needed
  }

  /**
   * Migrate categories from string[] to string[][]
   */
  async migrateForward(): Promise<MigrationResult> {
    console.log('开始正向迁移: string[] -> string[][]');
    
    const cursor = this.collection.find({
      category: { $exists: true, $ne: null }
    });

    let migratedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;
    let totalFound = 0;
    const details: string[] = [];

    for await (const doc of cursor) {
      totalFound++;
      
      try {
        if (Array.isArray(doc.category) && doc.category.length > 0) {
          // Check if already in new format
          if (Array.isArray(doc.category[0])) {
            details.push(`跳过已迁移: ${doc._id}`);
            skippedCount++;
            continue;
          }
          
          // Convert string[] to string[][]
          // Wrap the entire array as the first element of a new array
          const oldCategory = doc.category as string[];
          const newCategory = [oldCategory];
          
          await this.collection.updateOne(
            { _id: doc._id },
            { $set: { category: newCategory } }
          );
          
          details.push(`迁移: ${doc._id} | ${JSON.stringify(oldCategory)} -> ${JSON.stringify(newCategory)}`);
          migratedCount++;
        } else {
          // Handle empty/null categories
          await this.collection.updateOne(
            { _id: doc._id },
            { $set: { category: [] } }
          );
          details.push(`设置空分类: ${doc._id}`);
          migratedCount++;
        }
      } catch (error) {
        details.push(`错误: ${doc._id} | ${error.message}`);
        errorCount++;
      }
    }

    return {
      message: '正向迁移完成',
      totalFound,
      migrated: migratedCount,
      skipped: skippedCount,
      errors: errorCount,
      details
    };
  }

  /**
   * Rollback migration from string[][] to string[]
   */
  async rollback(): Promise<MigrationResult> {
    console.log('开始回滚迁移: string[][] -> string[]');
    
    const cursor = this.collection.find({
      category: { $exists: true, $ne: null }
    });

    let migratedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;
    let totalFound = 0;
    const details: string[] = [];

    for await (const doc of cursor) {
      totalFound++;
      
      try {
        if (Array.isArray(doc.category) && doc.category.length > 0) {
          // Check if in new format (string[][])
          if (Array.isArray(doc.category[0])) {
            // Convert string[][] to string[]
            // Extract the first element (which should be the original string array)
            const oldCategory = doc.category as string[][];
            const newCategory = oldCategory[0];
            
            await this.collection.updateOne(
              { _id: doc._id },
              { $set: { category: newCategory } }
            );
            
            details.push(`回滚: ${doc._id} | ${JSON.stringify(oldCategory)} -> ${JSON.stringify(newCategory)}`);
            migratedCount++;
          } else {
            details.push(`跳过已回滚: ${doc._id}`);
            skippedCount++;
          }
        }
      } catch (error) {
        details.push(`错误: ${doc._id} | ${error.message}`);
        errorCount++;
      }
    }

    return {
      message: '回滚迁移完成',
      totalFound,
      migrated: migratedCount,
      skipped: skippedCount,
      errors: errorCount,
      details
    };
  }

  /**
   * Validate migration results
   */
  async validate(): Promise<any> {
    const totalDocs = await this.collection.countDocuments();
    const newFormatDocs = await this.collection.countDocuments({
      'category.0': { $type: 'array' }
    });
    const oldFormatDocs = await this.collection.countDocuments({
      'category.0': { $type: 'string' }
    });
    const emptyCategories = await this.collection.countDocuments({
      $or: [
        { category: { $exists: false } },
        { category: null },
        { category: [] }
      ]
    });

    return {
      totalDocuments: totalDocs,
      newFormat: newFormatDocs,
      oldFormat: oldFormatDocs,
      emptyCategories,
      migrationComplete: oldFormatDocs === 0
    };
  }
}

// Export for use in NestJS service or standalone execution
export { CategoryMigration, MigrationResult };
